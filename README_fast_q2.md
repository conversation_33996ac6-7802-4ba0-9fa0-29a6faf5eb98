# 快速光伏发电功率预测 - 增强版

## 概述

这是一个针对4090显卡优化的快速光伏发电功率预测系统，包含多种机器学习算法的快速实现版本。相比原版本，新增了大量模型和精美的可视化功能。

## 🚀 新增特性

### 📊 **多种快速算法**
1. **基准模型**
   - Persistence模型（前一天同时刻预测）

2. **梯度提升模型**
   - LightGBM（CPU优化版）
   - XGBoost（多线程优化）
   - CatBoost（高性能版本）

3. **集成学习模型**
   - 随机森林（RandomForest）
   - 极端随机树（ExtraTrees）
   - 梯度提升回归（GradientBoosting）

4. **线性模型**
   - Ridge回归
   - Lasso回归
   - ElasticNet回归

5. **深度学习模型**
   - 快速神经网络（FastNN）

6. **模型集成**
   - 加权平均集成

### 🎨 **精美可视化**
1. **模型性能对比图**
   - 多指标柱状图对比
   - 综合排名热力图
   - 模型性能雷达图

2. **预测效果分析**
   - 散点图矩阵（真实值vs预测值）
   - 误差分布小提琴图
   - 时间序列预测对比图

3. **时间戳可视化**
   - 真实时间戳显示
   - 白昼时段背景标记
   - 多模型预测对比

### 📈 **评估指标**
- **MAE** (平均绝对误差)
- **RMSE** (均方根误差)
- **nRMSE** (归一化均方根误差)
- **SMAPE** (对称平均绝对百分比误差)
- **R²** (决定系数)
- **运行时间** (模型训练耗时)

## 🗂️ 输出文件

### 数据表格
```
Q2_fast_results/
├── daytime_model_evaluation.csv          # 模型性能评估
├── enhanced_model_summary.csv            # 增强模型总结
├── model_runtime.csv                     # 模型运行时间
├── prediction_results_table2.csv         # 完整预测结果
├── prediction_results_daytime_only.csv   # 仅白昼时段结果
└── *_feature_importance.csv              # 各模型特征重要性
```

### 可视化图表
```
Q2_fast_results/
├── enhanced_model_comparison.png         # 增强模型对比图
├── scatter_plot_matrix_enhanced.png      # 散点图矩阵
├── error_distribution_violin.png         # 误差分布小提琴图
├── model_performance_radar_enhanced.png  # 性能雷达图
├── time_series_with_timestamps.png       # 时间序列图
├── prediction_comparison_with_daytime.png # 预测对比图
└── *_feature_importance.png              # 特征重要性图
```

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install -r requirements_fast.txt
```

### 2. 运行程序
```bash
python fast_q2.py
```

### 3. 查看结果
程序运行完成后，所有结果将保存在 `Q2_fast_results/` 目录中。

## ⚙️ 4090优化配置

### GPU设置
```python
# 自动检测GPU并优化参数
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 4090优化参数
BATCH_SIZE = 1024      # 充分利用24GB显存
NUM_WORKERS = 16       # 多线程处理
PIN_MEMORY = True      # 内存映射优化
```

### 快速配置
```python
FAST_CONFIG = {
    'lgbm_rounds': 300,         # LightGBM训练轮数
    'xgb_estimators': 300,      # XGBoost估计器数量
    'catboost_iterations': 300, # CatBoost迭代次数
    'rf_estimators': 100,       # RandomForest估计器数量
    'nn_epochs': 50,            # 神经网络训练轮数
}
```

## 📊 预期性能

### 模型数量
- **12+个不同算法**：从简单线性模型到复杂深度学习
- **自动集成**：智能加权平均多个模型

### 运行速度
- **总耗时**：通常在5-15分钟内完成（取决于硬件）
- **并行优化**：充分利用多核CPU和GPU加速

### 预测精度
- **R²通常>0.85**：高质量的拟合效果
- **MAE通常<0.1MW**：较低的预测误差
- **相比基准改进**：通常有20-40%的性能提升

## 🎯 特色功能

### 1. 智能错误处理
- 自动跳过无法运行的模型
- 详细的错误信息输出
- 程序不会因单个模型失败而中断

### 2. 综合排名系统
- 多指标综合评分
- 自动识别最佳模型
- 性能改进百分比计算

### 3. 丰富的统计信息
```
🏆 最佳模型: XGBoost
   MAE: 0.0856 MW
   R²: 0.8734
   相比基准模型MAE改进: 23.4%

📊 共测试了 12 个模型
📈 生成了 8 个可视化图表
📋 保存了 6 个数据表格
```

## 🔧 自定义配置

### 修改模型参数
可以在代码中调整 `FAST_CONFIG` 字典来改变模型参数：

```python
FAST_CONFIG = {
    'lgbm_rounds': 500,      # 增加训练轮数提高精度
    'xgb_estimators': 500,   # 增加估计器数量
    'nn_epochs': 100,        # 增加神经网络训练轮数
}
```

### 添加新模型
可以轻松添加新的快速模型：

```python
def your_fast_model(X_train, y_train, X_test):
    # 实现你的模型
    model = YourModel()
    model.fit(X_train, y_train)
    predictions = model.predict(X_test)
    return predictions
```

## ⚠️ 注意事项

1. **内存使用**：某些模型可能需要较大内存
2. **依赖包**：确保安装了所有必需的包
3. **数据格式**：确保数据文件格式正确
4. **白昼评估**：所有指标仅在白昼时段(6:00-18:00)计算

## 🆚 与完整版对比

| 特性 | fast_q2.py | q2_4090.py |
|------|------------|------------|
| 模型数量 | 12+ | 3 |
| 训练时间 | 5-15分钟 | 30-60分钟 |
| 可视化图表 | 8个 | 10个 |
| 深度学习 | 简单NN | 高级TCN |
| 特征工程 | 基础版 | 增强版 |
| 适用场景 | 快速验证 | 深度分析 |

## 🎉 总结

这个增强版的快速预测系统提供了：
- **多样化的算法选择**
- **精美的可视化效果**
- **详细的性能分析**
- **4090显卡优化**
- **用户友好的输出**

非常适合快速验证不同算法的效果，为后续深度分析提供参考。
