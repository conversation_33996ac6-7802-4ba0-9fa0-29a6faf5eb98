import os
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
import pmdarima as pm
from prophet import Prophet
from sklearn.metrics import mean_absolute_error, r2_score
from tqdm import tqdm
import pickle
import time
import lightgbm as lgb
from xgboost import XGBRegressor
from sklearn.preprocessing import StandardScaler
import shap
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.ensemble import StackingRegressor
from sklearn.linear_model import LinearRegression, Ridge

# 设置中文字体和图表风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建结果目录
RESULTS_DIR = 'Q2_4090_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 4090显卡优化设置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {DEVICE}")

# 针对4090的优化参数
if torch.cuda.is_available():
    # 启用混合精度训练
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    # 设置更大的批次大小以充分利用4090的显存
    BATCH_SIZE = 1024  # 4090有24GB显存，可以设置更大
    # 增加工作线程数
    NUM_WORKERS = 16   # 4090配合高端CPU可以设置更多线程
    # 启用内存映射
    PIN_MEMORY = True
else:
    BATCH_SIZE = 64
    NUM_WORKERS = 4
    PIN_MEMORY = False

print(f"批次大小: {BATCH_SIZE}, 工作线程数: {NUM_WORKERS}")

# 数据处理函数
def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])

    # 添加时间相关特征
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['day'] = df['date_time'].dt.day
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek
    df['date'] = df['date_time'].dt.date

    # 添加时间段标记（96个时间点）
    df['time_point'] = df['hour'] * 4 + df['minute'] // 15

    # 添加白昼标记 (6点到18点之间视为白昼)
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] <= 18)).astype(int)

    return df

def prepare_features(df, lag_days=[1, 2, 7], window_sizes=[12, 24, 96]):
    """准备预测特征 - 4090优化版本，增加更多特征"""
    # 复制数据，避免修改原始数据
    df_features = df.copy()

    # 1. 滞后功率特征 (增加一周前的数据)
    for day in lag_days:
        lag_points = day * 96  # 每天96个时间点
        df_features[f'power_lag_{day}d'] = df_features['power'].shift(lag_points)

    # 2. 滑动统计特征 (增加更多窗口大小)
    for window in window_sizes:
        # 滑动平均
        df_features[f'power_mean_{window}'] = df_features['power'].rolling(window=window, min_periods=1).mean()
        # 滑动标准差
        df_features[f'power_std_{window}'] = df_features['power'].rolling(window=window, min_periods=1).std()
        # 滑动最大值
        df_features[f'power_max_{window}'] = df_features['power'].rolling(window=window, min_periods=1).max()
        # 滑动最小值
        df_features[f'power_min_{window}'] = df_features['power'].rolling(window=window, min_periods=1).min()

    # 3. 季节性特征
    # 小时的周期性（一天内的周期）
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)

    # 月份的周期性（一年内的周期）
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)

    # 一周中的天
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)

    # 4. 气象特征处理
    weather_features = ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature',
                       'nwp_humidity', 'nwp_windspeed', 'nwp_pressure']

    for feature in weather_features:
        if feature in df_features.columns:
            # 滞后特征
            df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
            # 滑动平均
            df_features[f'{feature}_ma_12'] = df_features[feature].rolling(window=12, min_periods=1).mean()
            # 变化率
            df_features[f'{feature}_diff'] = df_features[feature].diff()

    # 5. 相对理论效率特征
    if 'nwp_globalirrad' in df_features.columns:
        # 使用简化的理论模型：理论功率 = 辐照度 * 常数
        df_features['theoretical_power'] = df_features['nwp_globalirrad'] * 0.85 / 1000
        # 避免除以0
        df_features['relative_efficiency'] = np.where(
            df_features['theoretical_power'] > 0,
            df_features['power'] / df_features['theoretical_power'],
            np.nan
        )
        df_features['relative_efficiency_lag_1d'] = df_features['relative_efficiency'].shift(96)

    # 6. 交互特征
    if 'nwp_globalirrad' in df_features.columns and 'nwp_temperature' in df_features.columns:
        df_features['irrad_temp_interaction'] = df_features['nwp_globalirrad'] * df_features['nwp_temperature']

    # 7. 处理缺失值
    df_features['missing_flag'] = df_features['power'].isna().astype(int)

    # 填充缺失值
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def get_train_test_split(df, test_months=[2, 5, 8, 11]):
    """按照题目要求划分训练集和测试集

    训练集：除2/5/8/11月最后一周外的数据
    测试集：2/5/8/11月最后一周数据
    """
    # 先按月份筛选出测试月份
    test_month_df = df[df['month'].isin(test_months)]

    # 对于每个测试月份，找出最后一周
    test_indices = []
    test_month_data = {}

    for month in test_months:
        month_df = df[df['month'] == month]
        if len(month_df) == 0:
            continue

        # 获取该月最大日期
        max_date = month_df['date'].max()

        # 获取最大日期前7天的数据作为测试集
        test_dates = [(max_date - timedelta(days=i)) for i in range(7)]
        month_test_indices = df[df['date'].isin(test_dates)].index
        test_indices.extend(month_test_indices)

        # 保存每个月的测试数据用于分别绘图
        test_month_data[month] = df[df['date'].isin(test_dates)]

    # 剩余数据作为训练集
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices, test_month_data

def evaluate_models_enhanced(y_true, predictions, is_daytime=None):
    """增强的模型评估函数，包含R²指标"""
    results = {}

    # 如果提供了白昼标记，只在白昼时段计算指标
    if is_daytime is not None:
        daytime_mask = is_daytime.astype(bool)
        y_true_eval = y_true[daytime_mask]
    else:
        y_true_eval = y_true
        daytime_mask = np.ones(len(y_true), dtype=bool)

    for model_name, y_pred in predictions.items():
        if is_daytime is not None:
            y_pred_eval = y_pred[daytime_mask]
        else:
            y_pred_eval = y_pred

        # 计算平均绝对误差 (MAE)
        mae = np.mean(np.abs(y_true_eval - y_pred_eval))

        # 计算均方根误差 (RMSE)
        rmse = np.sqrt(np.mean((y_true_eval - y_pred_eval) ** 2))

        # 计算归一化均方根误差 (nRMSE)
        nrmse = rmse / np.mean(y_true_eval) if np.mean(y_true_eval) > 0 else 0

        # 计算对称平均绝对百分比误差 (SMAPE)
        smape = 100 * np.mean(2 * np.abs(y_pred_eval - y_true_eval) / (np.abs(y_pred_eval) + np.abs(y_true_eval) + 1e-8))

        # 计算R²决定系数
        r2 = r2_score(y_true_eval, y_pred_eval)

        results[model_name] = {
            'MAE': mae,
            'RMSE': rmse,
            'nRMSE': nrmse,
            'SMAPE': smape,
            'R²': r2
        }

    # 转换为DataFrame以便于展示
    results_df = pd.DataFrame(results).T
    return results_df

def plot_prediction_comparison_with_timestamps(test_data, predictions, title, save_path):
    """绘制带真实时间戳的预测效果对比图"""
    plt.figure(figsize=(15, 8))

    # 获取时间戳
    timestamps = test_data['date_time'].values
    y_true = test_data['power'].values

    # 绘制真实值
    plt.plot(timestamps, y_true, 'k-', label='真实值', linewidth=2, alpha=0.8)

    # 绘制不同模型的预测值
    colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink']
    for i, (model_name, y_pred) in enumerate(predictions.items()):
        color = colors[i % len(colors)]
        plt.plot(timestamps, y_pred, '--', label=f'{model_name}预测值',
                linewidth=1.5, alpha=0.7, color=color)

    plt.title(title, fontsize=14)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('功率 (MW)', fontsize=12)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)
    plt.tight_layout()

    plt.savefig(save_path)
    plt.close()

def plot_monthly_predictions(test_month_data, predictions, station_id):
    """分月绘制预测结果"""
    month_names = {2: '2月', 5: '5月', 8: '8月', 11: '11月'}

    for month, month_data in test_month_data.items():
        if len(month_data) == 0:
            continue

        # 获取该月的预测结果
        month_indices = month_data.index
        month_predictions = {}
        for model_name, pred in predictions.items():
            # 找到对应的预测值
            month_pred = []
            for idx in month_indices:
                # 在全局测试集中找到对应位置
                global_pos = np.where(test_indices == idx)[0]
                if len(global_pos) > 0:
                    month_pred.append(pred[global_pos[0]])
            month_predictions[model_name] = np.array(month_pred)

        # 绘制该月的预测对比图
        if month_predictions:
            plot_prediction_comparison_with_timestamps(
                month_data.reset_index(drop=True),
                month_predictions,
                f'站点{station_id} {month_names[month]}预测效果对比',
                os.path.join(RESULTS_DIR, f'prediction_month_{month}.png')
            )

def create_comparison_tables(results_df, station_id):
    """创建对比分析表格"""
    # 1. 模型性能对比表
    performance_table = results_df.round(4)
    performance_table.to_csv(os.path.join(RESULTS_DIR, 'model_performance_comparison.csv'))

    # 2. 相对于基准模型的改进表
    if 'Persistence' in results_df.index:
        base_metrics = results_df.loc['Persistence']
        improvement_table = pd.DataFrame()

        for model in results_df.index:
            if model != 'Persistence':
                improvement = {}
                for metric in ['MAE', 'RMSE', 'nRMSE', 'SMAPE']:
                    base_val = base_metrics[metric]
                    model_val = results_df.loc[model, metric]
                    improvement[f'{metric}_改进(%)'] = (base_val - model_val) / base_val * 100

                # R²的改进用绝对差值
                improvement['R²_改进'] = results_df.loc[model, 'R²'] - base_metrics['R²']
                improvement_table = pd.concat([improvement_table, pd.DataFrame([improvement], index=[model])])

        improvement_table.to_csv(os.path.join(RESULTS_DIR, 'model_improvement_comparison.csv'))

    # 3. 模型排名表
    ranking_table = pd.DataFrame()
    for metric in results_df.columns:
        if metric == 'R²':
            # R²越大越好
            ranking = results_df[metric].rank(ascending=False)
        else:
            # 其他指标越小越好
            ranking = results_df[metric].rank(ascending=True)
        ranking_table[f'{metric}_排名'] = ranking.astype(int)

    ranking_table.to_csv(os.path.join(RESULTS_DIR, 'model_ranking.csv'))

    return performance_table, improvement_table, ranking_table

def plot_analysis_charts(results_df, y_true, predictions, test_data):
    """绘制各种分析图表"""

    # 1. 模型性能雷达图
    plt.figure(figsize=(10, 8))

    # 标准化指标用于雷达图（R²保持原值，其他指标取倒数并标准化）
    metrics = ['MAE', 'RMSE', 'nRMSE', 'SMAPE', 'R²']
    normalized_results = results_df[metrics].copy()

    for metric in ['MAE', 'RMSE', 'nRMSE', 'SMAPE']:
        # 取倒数并标准化到0-1
        max_val = normalized_results[metric].max()
        normalized_results[metric] = 1 - (normalized_results[metric] / max_val)

    # R²已经在0-1之间，直接使用

    # 绘制雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合

    fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

    colors = ['red', 'blue', 'green', 'orange', 'purple']
    for i, model in enumerate(normalized_results.index[:5]):  # 只显示前5个模型
        values = normalized_results.loc[model].tolist()
        values += values[:1]  # 闭合

        ax.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.set_title('模型性能雷达图', size=16, y=1.1)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'model_performance_radar.png'))
    plt.close()

    # 2. 误差分布箱线图
    plt.figure(figsize=(12, 6))

    error_data = []
    model_names = []

    for model_name, y_pred in predictions.items():
        errors = y_true - y_pred
        error_data.append(errors)
        model_names.append(model_name)

    plt.boxplot(error_data, labels=model_names)
    plt.title('各模型预测误差分布', fontsize=14)
    plt.xlabel('模型', fontsize=12)
    plt.ylabel('预测误差 (MW)', fontsize=12)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    plt.savefig(os.path.join(RESULTS_DIR, 'error_distribution_boxplot.png'))
    plt.close()

    # 3. 散点图矩阵
    n_models = min(4, len(predictions))  # 最多显示4个模型
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()

    model_list = list(predictions.keys())[:n_models]

    for i, model_name in enumerate(model_list):
        y_pred = predictions[model_name]

        axes[i].scatter(y_true, y_pred, alpha=0.6, s=20)
        axes[i].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        axes[i].set_xlabel('真实值 (MW)')
        axes[i].set_ylabel('预测值 (MW)')
        axes[i].set_title(f'{model_name} (R²={results_df.loc[model_name, "R²"]:.3f})')
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'scatter_plot_matrix.png'))
    plt.close()

    # 4. 时间序列残差图
    plt.figure(figsize=(15, 8))

    timestamps = test_data['date_time'].values

    for i, (model_name, y_pred) in enumerate(list(predictions.items())[:3]):  # 只显示前3个模型
        residuals = y_true - y_pred
        plt.subplot(3, 1, i+1)
        plt.plot(timestamps, residuals, alpha=0.7, linewidth=1)
        plt.axhline(y=0, color='red', linestyle='--', alpha=0.8)
        plt.title(f'{model_name} 残差时间序列')
        plt.ylabel('残差 (MW)')
        plt.grid(True, alpha=0.3)
        if i == 2:
            plt.xlabel('时间')
        plt.xticks(rotation=45)

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'residual_time_series.png'))
    plt.close()

def persistence_model(train_data, test_dates, time_points=96):
    """
    实现Persistence模型（基准线）
    使用前一天同一时刻的功率作为预测值
    """
    print("运行Persistence基准模型...")
    predictions = []

    # 对每个测试日期进行预测
    for test_date in test_dates:
        # 获取前一天的日期
        prev_day = test_date - timedelta(days=1)

        # 获取前一天的功率值
        prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values

        if len(prev_day_powers) == time_points:
            # 如果前一天有完整的数据，直接使用
            predictions.extend(prev_day_powers)
        else:
            # 如果前一天数据不完整，使用最近的有效日期
            for day_offset in range(2, 8):  # 尝试前2~7天
                prev_day = test_date - timedelta(days=day_offset)
                prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
                if len(prev_day_powers) == time_points:
                    predictions.extend(prev_day_powers)
                    break
            else:
                # 如果前7天都没有完整数据，使用训练集中同时刻的平均值
                for time_point in range(time_points):
                    time_point_avg = train_data[train_data['time_point'] == time_point]['power'].mean()
                    predictions.append(time_point_avg)

    return np.array(predictions)

def lgbm_model_4090(X_train, y_train, X_test, test_dates):
    """
    使用LightGBM模型进行预测 - 4090优化版本
    """
    print("运行LightGBM模型 (4090优化版)...")

    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'lgbm_models')
    os.makedirs(models_dir, exist_ok=True)

    # 保存特征重要性的目录
    feature_imp_dir = os.path.join(RESULTS_DIR, 'feature_importance')
    os.makedirs(feature_imp_dir, exist_ok=True)

    # 初始化结果和特征重要性
    predictions = np.zeros(len(X_test))
    feature_importance = {}

    # 4090优化的LightGBM参数
    params = {
        'boosting_type': 'gbdt',
        'objective': 'regression',
        'metric': 'mae',
        'learning_rate': 0.05,
        'num_leaves': 63,  # 增加叶子数
        'max_depth': 10,   # 增加深度
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1,
        'device': 'cpu',  # 使用CPU（GPU版本需要特殊编译）
        'num_threads': NUM_WORKERS
    }

    # 按时间点分组训练和预测
    for time_point in tqdm(range(96), desc="LightGBM模型训练"):
        # 筛选该时间点的训练数据
        train_mask = (X_train['time_point'] == time_point)
        if train_mask.sum() == 0:
            continue

        X_train_tp = X_train[train_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
        y_train_tp = y_train[train_mask]

        # 筛选该时间点的测试数据
        test_mask = (X_test['time_point'] == time_point)
        X_test_tp = X_test[test_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')

        # 模型路径
        model_path = os.path.join(models_dir, f'lgbm_model_tp{time_point}.txt')

        if os.path.exists(model_path):
            # 如果模型已存在，直接加载
            model = lgb.Booster(model_file=model_path)
        else:
            # 创建数据集
            train_data = lgb.Dataset(X_train_tp, label=y_train_tp)

            # 训练模型
            model = lgb.train(
                params,
                train_data,
                num_boost_round=1000,  # 减少训练轮数以加快速度
                callbacks=[lgb.log_evaluation(0)]  # 只保留日志回调
            )

            # 保存模型
            model.save_model(model_path)

        # 预测
        if len(X_test_tp) > 0:
            preds = model.predict(X_test_tp)
            predictions[test_mask] = preds

        # 获取特征重要性
        importances = model.feature_importance(importance_type='gain')
        feature_names = model.feature_name()

        # 累加每个时间点的特征重要性
        for feat_name, importance in zip(feature_names, importances):
            if feat_name in feature_importance:
                feature_importance[feat_name] += importance
            else:
                feature_importance[feat_name] = importance

    # 绘制特征重要性图
    plot_feature_importance(feature_importance, 'LightGBM', os.path.join(feature_imp_dir, 'lgbm_feature_importance.png'))

    # 保存特征重要性数据
    pd.DataFrame({
        'Feature': list(feature_importance.keys()),
        'Importance': list(feature_importance.values())
    }).sort_values(by='Importance', ascending=False).to_csv(
        os.path.join(feature_imp_dir, 'lgbm_feature_importance.csv'), index=False
    )

    return predictions, feature_importance

def plot_feature_importance(feature_importance, model_name, save_path):
    """绘制特征重要性图"""
    # 转换为DataFrame
    feat_imp = pd.DataFrame({
        'Feature': list(feature_importance.keys()),
        'Importance': list(feature_importance.values())
    }).sort_values(by='Importance', ascending=False).head(20)  # 显示前20个特征

    # 绘图
    plt.figure(figsize=(12, 8))
    sns.barplot(x='Importance', y='Feature', data=feat_imp)
    plt.title(f'{model_name}模型特征重要性', fontsize=14)
    plt.xlabel('重要性', fontsize=12)
    plt.ylabel('特征', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    plt.savefig(save_path)
    plt.close()

# 4090优化的深度学习模型
class AdvancedTCN(nn.Module):
    """4090优化的高级TCN模型"""
    def __init__(self, input_size, output_size, num_channels=[64, 64, 32], kernel_size=3, dropout=0.2):
        super(AdvancedTCN, self).__init__()

        # 增加网络深度和宽度
        self.conv1d_layers = nn.ModuleList()
        self.batch_norms = nn.ModuleList()
        self.dropouts = nn.ModuleList()

        in_channels = input_size
        for out_channels in num_channels:
            self.conv1d_layers.append(nn.Conv1d(in_channels, out_channels, kernel_size, padding=kernel_size//2))
            self.batch_norms.append(nn.BatchNorm1d(out_channels))
            self.dropouts.append(nn.Dropout(dropout))
            in_channels = out_channels

        # 注意力机制
        self.attention = nn.MultiheadAttention(num_channels[-1], num_heads=8, batch_first=True)

        # 输出层
        self.fc_layers = nn.Sequential(
            nn.Linear(num_channels[-1], 128),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(64, output_size)
        )

    def forward(self, x):
        # x shape: [batch, seq_len, features]
        x = x.permute(0, 2, 1)  # [batch, features, seq_len]

        # 卷积层
        for conv, bn, dropout in zip(self.conv1d_layers, self.batch_norms, self.dropouts):
            x = conv(x)
            x = bn(x)
            x = torch.relu(x)
            x = dropout(x)

        # 转换为注意力机制的输入格式
        x = x.permute(0, 2, 1)  # [batch, seq_len, features]

        # 注意力机制
        attn_output, _ = self.attention(x, x, x)

        # 全局平均池化
        x = torch.mean(attn_output, dim=1)  # [batch, features]

        # 输出层
        x = self.fc_layers(x)

        return x

class TimeSeriesDataset4090(Dataset):
    """4090优化的时间序列数据集"""
    def __init__(self, X, y, seq_length=24):
        self.X = X
        self.y = y
        self.seq_length = seq_length

    def __len__(self):
        return max(0, len(self.X) - self.seq_length + 1)

    def __getitem__(self, idx):
        if idx + self.seq_length > len(self.X):
            # 处理边界情况
            X_seq = np.zeros((self.seq_length, self.X.shape[1]))
            available_len = len(self.X) - idx
            X_seq[:available_len] = self.X[idx:idx+available_len]
            X_seq[available_len:] = self.X[-1]  # 用最后一个值填充
        else:
            X_seq = self.X[idx:idx+self.seq_length]

        y_target = self.y[min(idx+self.seq_length-1, len(self.y)-1)]
        return torch.FloatTensor(X_seq), torch.FloatTensor([y_target])

def advanced_tcn_model_4090(X_train, y_train, X_test):
    """
    使用高级TCN模型进行预测 - 4090优化版本
    """
    print("运行高级TCN深度学习模型 (4090优化版)...")

    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'tcn_models')
    os.makedirs(models_dir, exist_ok=True)

    # 初始化结果
    predictions = np.zeros(len(X_test))

    # 特征列
    feature_cols = [col for col in X_train.columns if col not in ['time_point', 'date', 'date_time', 'power']]

    # 数据标准化
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()

    # 按时间点训练和预测
    for time_point in tqdm(range(96), desc="高级TCN模型训练"):
        # 筛选该时间点的训练数据
        train_mask = (X_train['time_point'] == time_point)
        if train_mask.sum() <= 24:  # 需要足够的序列长度
            continue

        # 筛选特征
        X_train_tp = X_train.loc[train_mask, feature_cols].values
        y_train_tp = y_train[train_mask]

        # 标准化数据
        X_train_scaled = scaler_X.fit_transform(X_train_tp)
        y_train_scaled = scaler_y.fit_transform(y_train_tp.reshape(-1, 1)).flatten()

        # 筛选该时间点的测试数据
        test_mask = (X_test['time_point'] == time_point)
        X_test_tp = X_test.loc[test_mask, feature_cols].values

        # 如果测试集为空，跳过
        if len(X_test_tp) == 0:
            continue

        # 标准化测试数据
        X_test_scaled = scaler_X.transform(X_test_tp)

        # 模型路径
        model_path = os.path.join(models_dir, f'advanced_tcn_model_tp{time_point}.pth')

        # 创建数据集和数据加载器
        seq_length = 24  # 使用24个时间步作为输入序列
        train_dataset = TimeSeriesDataset4090(X_train_scaled, y_train_scaled, seq_length)

        if len(train_dataset) == 0:
            continue

        train_loader = DataLoader(
            train_dataset,
            batch_size=BATCH_SIZE,
            shuffle=True,
            num_workers=NUM_WORKERS,
            pin_memory=PIN_MEMORY
        )

        # 创建模型
        model = AdvancedTCN(
            input_size=len(feature_cols),
            output_size=1,
            num_channels=[128, 128, 64],  # 增加网络容量
            kernel_size=3,
            dropout=0.2
        )
        model.to(DEVICE)

        if os.path.exists(model_path):
            # 如果模型已存在，直接加载
            model.load_state_dict(torch.load(model_path, map_location=DEVICE))
            model.eval()
        else:
            # 训练参数
            criterion = nn.MSELoss()
            optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-5)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)

            # 使用混合精度训练（4090优化）
            scaler = torch.cuda.amp.GradScaler() if DEVICE.type == 'cuda' else None

            epochs = 100
            best_loss = float('inf')
            patience_counter = 0

            # 训练
            model.train()
            for epoch in range(epochs):
                running_loss = 0.0
                for batch_X, batch_y in train_loader:
                    batch_X, batch_y = batch_X.to(DEVICE), batch_y.to(DEVICE)

                    optimizer.zero_grad()

                    if scaler is not None:
                        # 混合精度训练
                        with torch.cuda.amp.autocast():
                            outputs = model(batch_X)
                            loss = criterion(outputs, batch_y)

                        scaler.scale(loss).backward()
                        scaler.step(optimizer)
                        scaler.update()
                    else:
                        outputs = model(batch_X)
                        loss = criterion(outputs, batch_y)
                        loss.backward()
                        optimizer.step()

                    running_loss += loss.item()

                avg_loss = running_loss / len(train_loader)
                scheduler.step(avg_loss)

                # 早停
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    patience_counter = 0
                    # 保存最佳模型
                    torch.save(model.state_dict(), model_path)
                else:
                    patience_counter += 1
                    if patience_counter >= 20:
                        break

        # 预测
        model.eval()
        with torch.no_grad():
            test_predictions = []
            for i in range(len(X_test_tp)):
                # 构建输入序列
                if i < seq_length:
                    # 对于序列开始的部分，用重复填充
                    seq = np.repeat(X_test_scaled[i:i+1], seq_length, axis=0)
                else:
                    seq = X_test_scaled[i-seq_length+1:i+1]

                seq_tensor = torch.FloatTensor(seq).unsqueeze(0).to(DEVICE)

                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        pred = model(seq_tensor)
                else:
                    pred = model(seq_tensor)

                pred_value = pred.item()

                # 反标准化
                pred_value = scaler_y.inverse_transform([[pred_value]])[0][0]

                test_predictions.append(max(0, pred_value))  # 确保功率非负

            # 存储预测结果
            test_indices_list = test_mask[test_mask].index.tolist()
            for i, idx in enumerate(test_indices_list):
                if i < len(test_predictions):
                    global_pos = np.where(X_test.index == idx)[0]
                    if len(global_pos) > 0:
                        predictions[global_pos[0]] = test_predictions[i]

    return predictions

def save_prediction_results(test_data, predictions, station_id):
    """保存预测结果到表格文件"""
    # 创建结果表格
    results_table = pd.DataFrame()
    results_table['时间戳'] = test_data['date_time']
    results_table['实际功率(MW)'] = test_data['power']

    # 添加各模型的预测结果
    for model_name, pred in predictions.items():
        results_table[f'{model_name}_预测功率(MW)'] = pred

    # 计算预测误差
    for model_name, pred in predictions.items():
        results_table[f'{model_name}_绝对误差(MW)'] = np.abs(test_data['power'].values - pred)
        results_table[f'{model_name}_相对误差(%)'] = np.abs(test_data['power'].values - pred) / (test_data['power'].values + 1e-8) * 100

    # 保存到CSV文件
    results_table.to_csv(os.path.join(RESULTS_DIR, f'station{station_id}_prediction_results.csv'), index=False)

    return results_table

def generate_analysis_report(results_df, station_id, test_month_data):
    """生成分析报告"""
    report_lines = []
    report_lines.append(f"# 站点{station_id}光伏电站发电功率日前预测分析报告")
    report_lines.append("")
    report_lines.append("## 1. 模型性能总结")
    report_lines.append("")

    # 性能排名
    best_model_mae = results_df['MAE'].idxmin()
    best_model_r2 = results_df['R²'].idxmax()

    report_lines.append(f"- **最佳MAE模型**: {best_model_mae} (MAE: {results_df.loc[best_model_mae, 'MAE']:.4f} MW)")
    report_lines.append(f"- **最佳R²模型**: {best_model_r2} (R²: {results_df.loc[best_model_r2, 'R²']:.4f})")
    report_lines.append("")

    # 模型性能表格
    report_lines.append("## 2. 详细性能指标")
    report_lines.append("")
    report_lines.append("| 模型 | MAE | RMSE | nRMSE | SMAPE | R² |")
    report_lines.append("|------|-----|------|-------|-------|-----|")

    for model in results_df.index:
        mae = results_df.loc[model, 'MAE']
        rmse = results_df.loc[model, 'RMSE']
        nrmse = results_df.loc[model, 'nRMSE']
        smape = results_df.loc[model, 'SMAPE']
        r2 = results_df.loc[model, 'R²']
        report_lines.append(f"| {model} | {mae:.4f} | {rmse:.4f} | {nrmse:.4f} | {smape:.2f}% | {r2:.4f} |")

    report_lines.append("")

    # 测试数据统计
    report_lines.append("## 3. 测试数据统计")
    report_lines.append("")
    total_points = sum(len(data) for data in test_month_data.values())
    report_lines.append(f"- **测试总时间点数**: {total_points}")
    report_lines.append(f"- **测试月份**: {list(test_month_data.keys())}")

    for month, data in test_month_data.items():
        if len(data) > 0:
            avg_power = data['power'].mean()
            max_power = data['power'].max()
            report_lines.append(f"- **{month}月**: {len(data)}个时间点, 平均功率: {avg_power:.2f} MW, 最大功率: {max_power:.2f} MW")

    report_lines.append("")

    # 结论和建议
    report_lines.append("## 4. 结论和建议")
    report_lines.append("")

    if 'LightGBM' in results_df.index and results_df.loc['LightGBM', 'MAE'] == results_df['MAE'].min():
        report_lines.append("- **LightGBM模型表现最佳**，建议在实际应用中优先考虑")
    elif 'AdvancedTCN' in results_df.index and results_df.loc['AdvancedTCN', 'R²'] == results_df['R²'].max():
        report_lines.append("- **高级TCN深度学习模型在拟合度方面表现最佳**，适合复杂模式识别")

    report_lines.append("- 所有模型均在白昼时段进行评估，符合光伏发电特性")
    report_lines.append("- 建议结合多个模型进行集成预测以提高稳定性")
    report_lines.append("")

    # 保存报告
    with open(os.path.join(RESULTS_DIR, f'station{station_id}_analysis_report.md'), 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

    return '\n'.join(report_lines)

# 主函数
if __name__ == "__main__":
    print("正在执行光伏电站日前发电功率预测（4090优化版）...")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    # 使用站点1的数据
    station_id = 1

    # 加载数据
    start_time = time.time()
    df = load_station_data(station_id)
    print(f"成功加载站点{station_id}数据，共{len(df)}条记录")

    # 准备特征
    df_features = prepare_features(df)
    print(f"特征工程完成，共{df_features.shape[1]}个特征")

    # 划分训练集和测试集
    train_indices, test_indices, test_month_data = get_train_test_split(df_features)
    X_train = df_features.loc[train_indices]
    X_test = df_features.loc[test_indices]
    print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")

    # 获取测试集的日期列表
    test_dates = sorted(X_test['date'].unique())
    print(f"测试日期: {test_dates}")

    # 保存白昼标记，用于评估
    is_daytime = X_test['is_daytime'].values

    # 准备标签
    y_train = X_train['power'].values
    y_test = X_test['power'].values

    # 存储所有模型的预测结果
    predictions = {}
    model_times = {}

    # 1. 运行Persistence基准模型
    print("\n=== 运行Persistence基准模型 ===")
    start_time = time.time()
    persistence_pred = persistence_model(X_train, test_dates)
    persistence_time = time.time() - start_time
    predictions['Persistence'] = persistence_pred
    model_times['Persistence'] = persistence_time
    print(f"Persistence模型完成，耗时: {persistence_time:.2f}秒")

    # 2. 运行LightGBM模型
    print("\n=== 运行LightGBM模型 (4090优化版) ===")
    start_time = time.time()
    lgbm_pred, lgbm_feat_imp = lgbm_model_4090(X_train, y_train, X_test, test_dates)
    lgbm_time = time.time() - start_time
    predictions['LightGBM'] = lgbm_pred
    model_times['LightGBM'] = lgbm_time
    print(f"LightGBM模型完成，耗时: {lgbm_time:.2f}秒")

    # 3. 运行高级TCN深度学习模型
    print("\n=== 运行高级TCN深度学习模型 (4090优化版) ===")
    try:
        start_time = time.time()
        tcn_pred = advanced_tcn_model_4090(X_train, y_train, X_test)
        tcn_time = time.time() - start_time
        predictions['AdvancedTCN'] = tcn_pred
        model_times['AdvancedTCN'] = tcn_time
        print(f"高级TCN模型完成，耗时: {tcn_time:.2f}秒")
    except Exception as e:
        print(f"高级TCN模型运行出错: {e}")

    # 评估模型性能（仅在白昼时段）
    print("\n=== 模型性能评估 ===")
    results = evaluate_models_enhanced(y_test, predictions, is_daytime)
    print("所有模型评估结果(白昼时段):")
    print(results)

    # 保存评估结果
    results.to_csv(os.path.join(RESULTS_DIR, 'model_evaluation_4090.csv'))

    # 保存运行时间
    pd.DataFrame({'Runtime(s)': model_times}).to_csv(os.path.join(RESULTS_DIR, 'model_runtime_4090.csv'))

    # 创建对比分析表格
    print("\n=== 生成对比分析表格 ===")
    performance_table, improvement_table, ranking_table = create_comparison_tables(results, station_id)

    # 绘制分析图表
    print("\n=== 绘制分析图表 ===")
    plot_analysis_charts(results, y_test, predictions, X_test)

    # 绘制带时间戳的预测对比图
    print("\n=== 绘制预测对比图 ===")
    plot_prediction_comparison_with_timestamps(
        X_test, predictions,
        f'站点{station_id}光伏发电功率预测对比 (4090优化版)',
        os.path.join(RESULTS_DIR, 'prediction_comparison_4090.png')
    )

    # 分月绘制预测结果
    print("\n=== 分月绘制预测结果 ===")
    for month, month_data in test_month_data.items():
        if len(month_data) == 0:
            continue

        # 获取该月在测试集中的位置
        month_mask = X_test['month'] == month
        month_predictions = {}

        for model_name, pred in predictions.items():
            month_predictions[model_name] = pred[month_mask]

        # 绘制该月的预测对比图
        month_names = {2: '2月', 5: '5月', 8: '8月', 11: '11月'}
        plot_prediction_comparison_with_timestamps(
            X_test[month_mask],
            month_predictions,
            f'站点{station_id} {month_names[month]}预测效果对比',
            os.path.join(RESULTS_DIR, f'prediction_month_{month}_4090.png')
        )

    # 保存预测结果表格
    print("\n=== 保存预测结果表格 ===")
    results_table = save_prediction_results(X_test, predictions, station_id)

    # 生成分析报告
    print("\n=== 生成分析报告 ===")
    report = generate_analysis_report(results, station_id, test_month_data)

    print(f"\n=== 预测实验完成！ ===")
    print(f"所有结果已保存到: {RESULTS_DIR}")
    print(f"最佳模型 (MAE): {results['MAE'].idxmin()}")
    print(f"最佳模型 (R²): {results['R²'].idxmax()}")

    # 显示改进效果
    if 'Persistence' in results.index and len(results) > 1:
        base_mae = results.loc['Persistence', 'MAE']
        best_mae = results['MAE'].min()
        improvement = (base_mae - best_mae) / base_mae * 100
        print(f"相比基准模型MAE改进: {improvement:.1f}%")