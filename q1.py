import pandas as pd
import numpy as np
import matplotlib
# 设置非交互式后端，避免Qt错误
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime, timedelta
import matplotlib.dates as mdates
# 尝试导入地图绘制模块，如果不可用则跳过
try:
    import cartopy.crs as ccrs
    import cartopy.feature as cfeature
    HAS_CARTOPY = True
except ImportError:
    print("警告: 未安装cartopy包，将跳过地理地图绘制功能")
    HAS_CARTOPY = False
import warnings
warnings.filterwarnings('ignore')

# 不使用seaborn-whitegrid样式，改用基本样式设置
plt.style.use('default')
# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 中文支持
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建论文图表目录
os.makedirs('Q1_paper_figures', exist_ok=True)

# 读取站点数据
def load_data(station_id):
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['time_of_day'] = df['hour'] + df['minute']/60
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['day'] = df['date_time'].dt.day
    df['dayofyear'] = df['date_time'].dt.dayofyear
    return df

# 获取站点经纬度信息
def get_station_locations():
    # 这里应根据实际情况读取站点的经纬度信息
    # 示例数据，实际应用中应替换为真实数据
    locations = {
        0: {'name': '站点0', 'lat': 39.90, 'lon': 116.40},  # 示例坐标
        1: {'name': '站点1', 'lat': 31.23, 'lon': 121.47},
        2: {'name': '站点2', 'lat': 23.13, 'lon': 113.26},
        3: {'name': '站点3', 'lat': 30.58, 'lon': 114.31},
        4: {'name': '站点4', 'lat': 28.21, 'lon': 112.94},
        5: {'name': '站点5', 'lat': 22.54, 'lon': 114.06},
        6: {'name': '站点6', 'lat': 34.34, 'lon': 108.94},
        7: {'name': '站点7', 'lat': 36.06, 'lon': 120.38},
        8: {'name': '站点8', 'lat': 32.06, 'lon': 118.78},
        9: {'name': '站点9', 'lat': 30.25, 'lon': 120.17},
    }
    return locations

# 计算理论功率
def calculate_theoretical_power(df, capacity=None):
    if capacity is None:
        capacity = df['power'].max() * 1.2
    
    efficiency = 0.85
    df['theoretical_power'] = df['nwp_globalirrad'] * capacity / 1000 * efficiency
    df['relative_efficiency'] = np.where(df['theoretical_power'] > 0, 
                                         df['power'] / df['theoretical_power'], 
                                         np.nan)
    return df, capacity

# 读取汇总结果
try:
    summary_df = pd.read_csv('Q1_results/stations_summary.csv')
    print("成功读取汇总数据")
except Exception as e:
    print(f"读取汇总数据失败: {e}")
    # 创建一个基本的汇总数据框架
    summary_df = pd.DataFrame({
        'station_id': range(10),
        'capacity_factor': [0.13] * 10,
        'capacity_estimate': [20.0] * 10,
        'max_15min_ramp_pct': [60.0] * 10
    })

print("开始绘制论文图形...")

# 1. 季节性趋势对比图
def plot_seasonal_comparison(station_ids=[0, 4, 5]):
    print(f"绘制季节性趋势对比图 (站点 {station_ids})")
    plt.figure(figsize=(10, 6))
    
    for station_id in station_ids:
        try:
            df = load_data(station_id)
            # 按日期分组计算平均功率
            daily_avg = df.groupby(pd.to_datetime(df['date']))['power'].mean()
            
            # 获取容量
            if station_id in summary_df['station_id'].values:
                capacity = summary_df.loc[summary_df['station_id'] == station_id, 'capacity_estimate'].values[0]
            else:
                df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 7天移动平均
            ma7 = daily_avg.rolling(window=7, center=True).mean()
            
            # 标准化为容量百分比
            normalized = ma7 / capacity * 100
            
            plt.plot(normalized.index, normalized, 
                     label=f'站点{station_id} ({capacity:.1f}MW)', 
                     linewidth=2)
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.title('不同装机容量电站的季节性发电特性', fontsize=14)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('装机容量百分比 (%)', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(fontsize=10)
    plt.tight_layout()
    
    try:
        plt.savefig('Q1_paper_figures/seasonal_comparison.png')
        print("  图表已保存")
    except Exception as e:
        print(f"  保存图表失败: {e}")
    plt.close()

# 2. 典型日模式分析图
def plot_typical_day_patterns(station_ids=[1, 4]):
    print(f"绘制典型日模式分析图 (站点 {station_ids})")
    plt.figure(figsize=(12, 10))
    
    for i, station_id in enumerate(station_ids):
        try:
            df = load_data(station_id)
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 找出完整的日数据
            daily_patterns = []
            dates = []
            
            for date, group in df.groupby('date'):
                if len(group) >= 96:  # 假设完整一天有96个数据点
                    daily_pattern = group.sort_values('date_time').iloc[:96]['power'].values
                    daily_patterns.append(daily_pattern)
                    dates.append(date)
            
            if len(daily_patterns) > 0:
                daily_patterns = np.array(daily_patterns)
                
                # 按照总发电量归一化
                normalized_patterns = daily_patterns / capacity
                
                # 计算每天的总发电量
                daily_sum = np.sum(daily_patterns, axis=1)
                
                # 选择三种典型日：晴天(高发电量)、多云(中等发电量)、阴天(低发电量)
                # 使用三分位数来分类
                low_tercile = np.percentile(daily_sum, 33.3)
                high_tercile = np.percentile(daily_sum, 66.7)
                
                cloudy_idx = np.where(daily_sum <= low_tercile)[0]
                partly_cloudy_idx = np.where((daily_sum > low_tercile) & (daily_sum <= high_tercile))[0]
                sunny_idx = np.where(daily_sum > high_tercile)[0]
                
                plt.subplot(len(station_ids), 1, i+1)
                
                # 创建具体时间点而不是简单的0-24小时范围
                reference_date = datetime(2023, 1, 1)
                time_points = [reference_date + timedelta(minutes=15*i) for i in range(96)]
                
                if len(sunny_idx) > 0:
                    sunny_patterns = normalized_patterns[sunny_idx]
                    mean_sunny = np.mean(sunny_patterns, axis=0)
                    p10_sunny = np.percentile(sunny_patterns, 10, axis=0)
                    p90_sunny = np.percentile(sunny_patterns, 90, axis=0)
                    plt.plot(time_points, mean_sunny, 'r-', label='晴天', linewidth=2)
                    plt.fill_between(time_points, p10_sunny, p90_sunny, color='r', alpha=0.2)
                
                if len(partly_cloudy_idx) > 0:
                    partly_patterns = normalized_patterns[partly_cloudy_idx]
                    mean_partly = np.mean(partly_patterns, axis=0)
                    p10_partly = np.percentile(partly_patterns, 10, axis=0)
                    p90_partly = np.percentile(partly_patterns, 90, axis=0)
                    plt.plot(time_points, mean_partly, 'g-', label='多云', linewidth=2)
                    plt.fill_between(time_points, p10_partly, p90_partly, color='g', alpha=0.2)
                
                if len(cloudy_idx) > 0:
                    cloudy_patterns = normalized_patterns[cloudy_idx]
                    mean_cloudy = np.mean(cloudy_patterns, axis=0)
                    p10_cloudy = np.percentile(cloudy_patterns, 10, axis=0)
                    p90_cloudy = np.percentile(cloudy_patterns, 90, axis=0)
                    plt.plot(time_points, mean_cloudy, 'b-', label='阴天', linewidth=2)
                    plt.fill_between(time_points, p10_cloudy, p90_cloudy, color='b', alpha=0.2)
                
                plt.title(f'站点{station_id} - 典型日功率曲线模式 (装机容量 {capacity:.1f}MW)', fontsize=12)
                plt.xlabel('时间', fontsize=10)
                plt.ylabel('标准化功率 (占装机容量比例)', fontsize=10)
                
                # 设置x轴时间格式
                plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
                plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
                
                plt.grid(True, linestyle='--', alpha=0.7)
                plt.legend(fontsize=10)
            else:
                print(f"  站点{station_id}没有足够的完整日数据")
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.tight_layout()
    
    try:
        plt.savefig('Q1_paper_figures/typical_day_patterns.png')
        print("  图表已保存")
    except Exception as e:
        print(f"  保存图表失败: {e}")
    plt.close()

# 3. 辐照度-效率关系分析图
def plot_irradiance_efficiency(station_ids=[0, 3, 5, 9]):
    print(f"绘制辐照度-效率关系分析图 (站点 {station_ids})")
    plt.figure(figsize=(12, 10))
    
    for i, station_id in enumerate(station_ids):
        try:
            df = load_data(station_id)
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 按辐照度水平分组计算平均效率
            df_with_theory['irrad_bin'] = pd.cut(df_with_theory['nwp_globalirrad'], 
                                      bins=[0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, np.inf],
                                      labels=['0-100', '100-200', '200-300', '300-400', '400-500', 
                                              '500-600', '600-700', '700-800', '800-900', '900-1000', '1000+'])
            
            irrad_eff = df_with_theory.groupby('irrad_bin')['relative_efficiency'].mean().reset_index()
            
            plt.subplot(2, 2, i+1)
            plt.bar(range(len(irrad_eff)), irrad_eff['relative_efficiency'], color='skyblue')
            plt.axhline(y=1, color='r', linestyle='--')
            plt.title(f'站点{station_id} - 辐照度与效率关系 ({capacity:.1f}MW)', fontsize=12)
            plt.xlabel('辐照度范围 (W/m²)', fontsize=10)
            plt.ylabel('相对效率 (实际/理论)', fontsize=10)
            plt.xticks(range(len(irrad_eff)), irrad_eff['irrad_bin'], rotation=45, fontsize=8)
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.tight_layout()
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.suptitle('不同辐照度水平下的光伏电站相对效率', fontsize=14, y=1.02)
    plt.tight_layout()
    
    try:
        plt.savefig('Q1_paper_figures/irradiance_efficiency_comparison.png')
        print("  图表已保存")
    except Exception as e:
        print(f"  保存图表失败: {e}")
    plt.close()

# 4. 站点对比图
def plot_cf_ramprate_comparison():
    print("绘制容量因子和功率变化率对比图")
    try:
        fig, ax = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
        
        # 容量因子对比
        ax[0].bar(summary_df['station_id'], summary_df['capacity_factor'] * 100, color='skyblue')
        ax[0].set_title('各站点容量因子(CF)对比', fontsize=14)
        ax[0].set_ylabel('容量因子 (%)', fontsize=12)
        ax[0].grid(True, linestyle='--', alpha=0.7)
        
        # 变化率对比
        ax[1].bar(summary_df['station_id'], summary_df['max_15min_ramp_pct'], color='salmon')
        ax[1].set_title('各站点最大15分钟功率变化率对比', fontsize=14)
        ax[1].set_xlabel('站点ID', fontsize=12)
        ax[1].set_ylabel('最大功率变化率 (%)', fontsize=12)
        ax[1].grid(True, linestyle='--', alpha=0.7)
        
        plt.tight_layout()
        plt.savefig('Q1_paper_figures/cf_ramprate_comparison.png')
        print("  图表已保存")
    except Exception as e:
        print(f"  绘制对比图出错: {e}")
    plt.close()

# 5. 功率变化率的时间分布特征
def plot_ramprate_time_distribution(station_ids=[1, 5]):
    print(f"绘制功率变化率时间分布图 (站点 {station_ids})")
    plt.figure(figsize=(12, 10))
    
    for i, station_id in enumerate(station_ids):
        try:
            df = load_data(station_id)
            df_with_theory, capacity = calculate_theoretical_power(df)
            
            # 计算功率变化率
            df_with_theory['power_diff'] = df_with_theory.groupby('date')['power'].diff()
            df_with_theory['ramp_rate'] = df_with_theory['power_diff'] / 0.25  # 假设15分钟间隔
            df_with_theory['ramp_rate_pct'] = df_with_theory['ramp_rate'] / capacity * 100  # 转换为百分比
            
            # 按小时统计ramp rate的绝对值
            df_with_theory['abs_ramp_rate_pct'] = df_with_theory['ramp_rate_pct'].abs()
            hourly_ramp = df_with_theory.groupby('hour')['abs_ramp_rate_pct'].agg(['mean', 'std', 'max']).reset_index()
            
            plt.subplot(len(station_ids), 1, i+1)
            
            # 创建具体时间点而不是简单的小时数值
            reference_date = datetime(2023, 1, 1)
            time_points = [reference_date + timedelta(hours=h) for h in hourly_ramp['hour']]
            
            plt.errorbar(time_points, hourly_ramp['mean'], yerr=hourly_ramp['std'], 
                         fmt='o-', capsize=5, label='平均值±标准差')
            plt.plot(time_points, hourly_ramp['max'], 'r--', label='最大值')
            plt.title(f'站点{station_id} - 功率变化率的时间分布 ({capacity:.1f}MW)', fontsize=12)
            plt.xlabel('时间', fontsize=10)
            plt.ylabel('功率变化率 (% of 装机容量/小时)', fontsize=10)
            
            # 设置x轴时间格式
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            plt.gca().xaxis.set_major_locator(mdates.HourLocator(interval=2))
            
            plt.grid(True, linestyle='--', alpha=0.7)
            plt.legend(fontsize=10)
            plt.xlim(reference_date, reference_date + timedelta(hours=23))
        except Exception as e:
            print(f"  处理站点{station_id}时出错: {e}")
    
    plt.tight_layout()
    
    try:
        plt.savefig('Q1_paper_figures/ramprate_time_distribution.png')
        print("  图表已保存")
    except Exception as e:
        print(f"  保存图表失败: {e}")
    plt.close()

# 绘制站点地理分布图
def plot_station_geographical_distribution(summary_df):
    print("绘制站点地理分布图")
    try:
        # 获取站点位置信息
        locations = get_station_locations()
        
        plt.figure(figsize=(10, 8))
        
        # 提取所有站点的经纬度
        lons = [info['lon'] for station_id, info in locations.items() if station_id in summary_df['station_id'].values]
        lats = [info['lat'] for station_id, info in locations.items() if station_id in summary_df['station_id'].values]
        station_ids = [station_id for station_id in locations.keys() if station_id in summary_df['station_id'].values]
        
        # 获取对应的容量和容量因子
        capacities = []
        cfs = []
        for station_id in station_ids:
            station_data = summary_df[summary_df['station_id'] == station_id].iloc[0]
            capacities.append(station_data['capacity_estimate'])
            cfs.append(station_data['capacity_factor'] * 100)
        
        # 绘制散点图
        scatter = plt.scatter(lons, lats, 
                            s=[c*5 for c in capacities],  # 点大小与装机容量成正比
                            c=cfs,  # 颜色与容量因子对应
                            cmap='viridis',
                            alpha=0.7)
        
        # 添加站点标签
        for i, station_id in enumerate(station_ids):
            plt.text(lons[i], lats[i], f"站点{station_id}", 
                    fontsize=9, ha='center', va='bottom')
        
        # 添加颜色条和标题
        cbar = plt.colorbar(label='容量因子 (%)')
        plt.title('光伏电站地理分布与特性', fontsize=14)
        plt.xlabel('经度', fontsize=12)
        plt.ylabel('纬度', fontsize=12)
        plt.grid(True, linestyle='--', alpha=0.7)
        
        # 添加图例解释散点大小
        sizes = [10, 20, 30]
        labels = ['10MW', '20MW', '30MW']
        handles = []
        
        # 在图例中展示不同大小
        for size in sizes:
            handle = plt.scatter([], [], s=size*5, c='gray', alpha=0.7)
            handles.append(handle)
        
        plt.legend(handles, labels, title='装机容量', loc='best')
        
        plt.savefig('Q1_paper_figures/station_geographical_distribution.png')
        print("  地理分布图已保存")
    except Exception as e:
        print(f"  绘制地理分布图失败: {e}")
    plt.close()

# 执行绘图
try:
    plot_seasonal_comparison([0, 3, 5])
    plot_typical_day_patterns([1, 4])
    plot_irradiance_efficiency([0, 3, 5, 9])
    plot_cf_ramprate_comparison()
    plot_ramprate_time_distribution([1, 5])
    
    # 尝试读取汇总数据
    try:
        summary_df = pd.read_csv('Q1_results/stations_summary.csv')
        plot_station_geographical_distribution(summary_df)
    except Exception as e:
        print(f"无法加载站点汇总数据进行地理分布绘图: {e}")
    
    print("所有图表绘制完成，保存在 Q1_paper_figures 目录")
except Exception as e:
    print(f"绘图过程中遇到错误: {e}")