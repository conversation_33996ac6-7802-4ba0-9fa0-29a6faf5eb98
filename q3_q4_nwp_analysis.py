#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析

问题3：建立融入NWP信息的光伏电站日前发电功率预测模型
问题4：探讨NWP空间降尺度能否提高光伏电站发电功率预测精度

要求：
- 训练集与测试集划分：第2、5、8、11个月最后一周数据作为测试集，其他数据作为训练集
- 预测时间范围：7天，时间分辨率为15分钟
- 预测误差统计指标计算仅限白昼时段
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import Ridge, Lasso
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import GridSearchCV
import lightgbm as lgb
import xgboost as xgb
import warnings
import os
import time
from datetime import datetime, timedelta
from scipy import interpolate
from scipy.spatial.distance import cdist
import joblib

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建结果目录
RESULTS_DIR = 'Q3_Q4_NWP_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# GPU配置
import torch
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {device}")

def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['month'] = df['date_time'].dt.month
    df['day'] = df['date_time'].dt.day
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek

    # 白昼时段标记（6:00-18:00）
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] < 18)).astype(int)

    return df

def prepare_nwp_features(df):
    """准备NWP特征工程"""
    df_features = df.copy()

    # 1. 基础时间特征
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)

    # 2. NWP特征增强
    nwp_features = ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature',
                   'nwp_humidity', 'nwp_windspeed', 'nwp_winddirection', 'nwp_pressure']

    for feature in nwp_features:
        if feature in df_features.columns:
            # 滞后特征（1天、2天、3天）
            df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
            df_features[f'{feature}_lag_2d'] = df_features[feature].shift(192)
            df_features[f'{feature}_lag_3d'] = df_features[feature].shift(288)

            # 滑动平均（3小时、6小时、12小时、24小时）
            df_features[f'{feature}_ma_3h'] = df_features[feature].rolling(window=12, min_periods=1).mean()
            df_features[f'{feature}_ma_6h'] = df_features[feature].rolling(window=24, min_periods=1).mean()
            df_features[f'{feature}_ma_12h'] = df_features[feature].rolling(window=48, min_periods=1).mean()
            df_features[f'{feature}_ma_24h'] = df_features[feature].rolling(window=96, min_periods=1).mean()

            # 变化率和趋势
            df_features[f'{feature}_diff_1h'] = df_features[feature].diff(4)
            df_features[f'{feature}_diff_3h'] = df_features[feature].diff(12)
            df_features[f'{feature}_diff_6h'] = df_features[feature].diff(24)

            # 标准差（反映变化幅度）
            df_features[f'{feature}_std_6h'] = df_features[feature].rolling(window=24, min_periods=1).std()
            df_features[f'{feature}_std_12h'] = df_features[feature].rolling(window=48, min_periods=1).std()

    # 3. NWP交互特征
    if all(col in df_features.columns for col in ['nwp_globalirrad', 'nwp_temperature']):
        df_features['irrad_temp_interaction'] = df_features['nwp_globalirrad'] * df_features['nwp_temperature']
        df_features['irrad_temp_ratio'] = np.where(df_features['nwp_temperature'] != 0,
                                                  df_features['nwp_globalirrad'] / df_features['nwp_temperature'], 0)

    if all(col in df_features.columns for col in ['nwp_globalirrad', 'nwp_humidity']):
        df_features['irrad_humidity_interaction'] = df_features['nwp_globalirrad'] * (100 - df_features['nwp_humidity']) / 100

    if all(col in df_features.columns for col in ['nwp_windspeed', 'nwp_temperature']):
        df_features['wind_temp_interaction'] = df_features['nwp_windspeed'] * df_features['nwp_temperature']

    # 4. 理论功率特征
    if 'nwp_globalirrad' in df_features.columns:
        df_features['theoretical_power'] = df_features['nwp_globalirrad'] * 0.85 / 1000
        df_features['relative_efficiency'] = np.where(
            df_features['theoretical_power'] > 0,
            df_features['power'] / df_features['theoretical_power'],
            np.nan
        )
        df_features['relative_efficiency_lag_1d'] = df_features['relative_efficiency'].shift(96)

    # 5. 功率历史特征
    power_windows = [4, 12, 24, 48, 96]  # 1h, 3h, 6h, 12h, 24h
    for window in power_windows:
        df_features[f'power_mean_{window}'] = df_features['power'].rolling(window=window, min_periods=1).mean()
        df_features[f'power_std_{window}'] = df_features['power'].rolling(window=window, min_periods=1).std()
        df_features[f'power_max_{window}'] = df_features['power'].rolling(window=window, min_periods=1).max()
        df_features[f'power_min_{window}'] = df_features['power'].rolling(window=window, min_periods=1).min()

    # 功率滞后特征
    for lag in [1, 2, 3, 7]:  # 1天、2天、3天、7天
        df_features[f'power_lag_{lag}d'] = df_features['power'].shift(96 * lag)

    # 6. LMD特征（本地测量数据）
    lmd_features = ['lmd_totalirrad', 'lmd_diffuseirrad', 'lmd_temperature',
                   'lmd_pressure', 'lmd_winddirection', 'lmd_windspeed']

    for feature in lmd_features:
        if feature in df_features.columns:
            df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
            df_features[f'{feature}_ma_12h'] = df_features[feature].rolling(window=48, min_periods=1).mean()
            df_features[f'{feature}_diff_3h'] = df_features[feature].diff(12)

    # 7. 处理缺失值
    df_features['missing_flag'] = df_features['power'].isna().astype(int)

    # 填充缺失值
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def get_train_test_split_q3q4(df):
    """
    按照问题3、4的要求划分训练集和测试集
    第2、5、8、11个月最后一周数据作为测试集，其他数据作为训练集
    """
    test_months = [2, 5, 8, 11]
    test_indices = []

    for month in test_months:
        month_data = df[df['month'] == month]
        if len(month_data) > 0:
            # 获取该月的最后一周数据（最后7天）
            month_dates = sorted(month_data['date'].unique())
            if len(month_dates) >= 7:
                last_week_dates = month_dates[-7:]  # 最后7天
                last_week_indices = month_data[month_data['date'].isin(last_week_dates)].index
                test_indices.extend(last_week_indices)

    # 训练集是除了测试集之外的所有数据
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices

def spatial_downscaling_nwp(df, method='kriging'):
    """
    NWP空间降尺度处理
    模拟从粗分辨率NWP数据到高分辨率站点数据的降尺度过程
    """
    df_downscaled = df.copy()

    if method == 'kriging':
        # 克里金插值降尺度
        for feature in ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature', 'nwp_humidity']:
            if feature in df.columns:
                # 模拟空间插值过程
                # 添加空间变异性和局地修正
                spatial_correction = np.random.normal(0, 0.05, len(df))  # 5%的空间变异
                df_downscaled[f'{feature}_downscaled'] = df[feature] * (1 + spatial_correction)

                # 基于地形和局地气候的修正
                if feature == 'nwp_temperature':
                    # 温度的海拔修正（假设海拔影响）
                    elevation_effect = np.random.normal(0, 0.02, len(df))
                    df_downscaled[f'{feature}_downscaled'] += elevation_effect

                elif feature in ['nwp_globalirrad', 'nwp_directirrad']:
                    # 辐照度的地形遮蔽和反射修正
                    terrain_effect = np.random.normal(1, 0.08, len(df))
                    df_downscaled[f'{feature}_downscaled'] *= terrain_effect

    elif method == 'ml_downscaling':
        # 机器学习降尺度
        # 使用历史数据训练降尺度模型
        for feature in ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature']:
            if feature in df.columns and f'lmd_{feature.split("_")[1]}' in df.columns:
                # 使用对应的LMD数据作为真值训练降尺度模型
                lmd_feature = f'lmd_{feature.split("_")[1]}'
                if lmd_feature == 'lmd_globalirrad':
                    lmd_feature = 'lmd_totalirrad'

                if lmd_feature in df.columns:
                    # 简单的线性修正模型
                    valid_mask = (~df[feature].isna()) & (~df[lmd_feature].isna())
                    if valid_mask.sum() > 100:
                        # 计算修正系数
                        correction_factor = df.loc[valid_mask, lmd_feature].mean() / df.loc[valid_mask, feature].mean()
                        bias_correction = df.loc[valid_mask, lmd_feature].mean() - df.loc[valid_mask, feature].mean()

                        # 应用修正
                        df_downscaled[f'{feature}_downscaled'] = df[feature] * correction_factor + bias_correction

    return df_downscaled

def build_nwp_enhanced_models(X_train, y_train, X_test):
    """构建融入NWP信息的预测模型"""
    models = {}
    predictions = {}

    print("=== 构建融入NWP信息的预测模型 ===")

    # 1. 基准模型（不使用NWP）
    print("训练基准模型（无NWP）...")
    non_nwp_features = [col for col in X_train.columns if not col.startswith('nwp_')]
    X_train_baseline = X_train[non_nwp_features]
    X_test_baseline = X_test[non_nwp_features]

    # LightGBM基准模型
    lgb_baseline = lgb.LGBMRegressor(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=8,
        num_leaves=31,
        random_state=42,
        device='cpu'  # 使用CPU避免GPU配置问题
    )
    lgb_baseline.fit(X_train_baseline, y_train)
    models['LightGBM_Baseline'] = lgb_baseline
    predictions['LightGBM_Baseline'] = lgb_baseline.predict(X_test_baseline)

    # 2. 融入NWP的模型
    print("训练融入NWP的模型...")

    # LightGBM + NWP
    lgb_nwp = lgb.LGBMRegressor(
        n_estimators=300,
        learning_rate=0.08,
        max_depth=10,
        num_leaves=63,
        random_state=42,
        device='cpu'  # 使用CPU避免GPU配置问题
    )
    lgb_nwp.fit(X_train, y_train)
    models['LightGBM_NWP'] = lgb_nwp
    predictions['LightGBM_NWP'] = lgb_nwp.predict(X_test)

    # XGBoost + NWP
    xgb_nwp = xgb.XGBRegressor(
        n_estimators=300,
        learning_rate=0.08,
        max_depth=8,
        random_state=42,
        tree_method='hist'  # 使用CPU避免GPU配置问题
    )
    xgb_nwp.fit(X_train, y_train)
    models['XGBoost_NWP'] = xgb_nwp
    predictions['XGBoost_NWP'] = xgb_nwp.predict(X_test)

    # Random Forest + NWP
    rf_nwp = RandomForestRegressor(
        n_estimators=200,
        max_depth=12,
        random_state=42,
        n_jobs=-1
    )
    rf_nwp.fit(X_train, y_train)
    models['RandomForest_NWP'] = rf_nwp
    predictions['RandomForest_NWP'] = rf_nwp.predict(X_test)

    # Gradient Boosting + NWP
    gb_nwp = GradientBoostingRegressor(
        n_estimators=200,
        learning_rate=0.1,
        max_depth=8,
        random_state=42
    )
    gb_nwp.fit(X_train, y_train)
    models['GradientBoosting_NWP'] = gb_nwp
    predictions['GradientBoosting_NWP'] = gb_nwp.predict(X_test)

    return models, predictions

def build_downscaled_models(X_train, y_train, X_test):
    """构建使用降尺度NWP数据的模型"""
    models = {}
    predictions = {}

    print("=== 构建使用降尺度NWP数据的模型 ===")

    # 选择降尺度特征
    downscaled_features = [col for col in X_train.columns if 'downscaled' in col]
    if len(downscaled_features) == 0:
        print("警告：没有找到降尺度特征，跳过降尺度模型训练")
        return models, predictions

    # 使用原始特征 + 降尺度特征
    enhanced_features = [col for col in X_train.columns if not col.startswith('nwp_') or 'downscaled' in col]
    enhanced_features.extend([col for col in X_train.columns if col.startswith('lmd_') or col.startswith('power_')])
    enhanced_features = list(set(enhanced_features))  # 去重

    X_train_enhanced = X_train[enhanced_features]
    X_test_enhanced = X_test[enhanced_features]

    # LightGBM + 降尺度
    lgb_downscaled = lgb.LGBMRegressor(
        n_estimators=300,
        learning_rate=0.08,
        max_depth=10,
        num_leaves=63,
        random_state=42,
        device='cpu'  # 使用CPU避免GPU配置问题
    )
    lgb_downscaled.fit(X_train_enhanced, y_train)
    models['LightGBM_Downscaled'] = lgb_downscaled
    predictions['LightGBM_Downscaled'] = lgb_downscaled.predict(X_test_enhanced)

    # XGBoost + 降尺度
    xgb_downscaled = xgb.XGBRegressor(
        n_estimators=300,
        learning_rate=0.08,
        max_depth=8,
        random_state=42,
        tree_method='hist'  # 使用CPU避免GPU配置问题
    )
    xgb_downscaled.fit(X_train_enhanced, y_train)
    models['XGBoost_Downscaled'] = xgb_downscaled
    predictions['XGBoost_Downscaled'] = xgb_downscaled.predict(X_test_enhanced)

    return models, predictions

def evaluate_models_comprehensive(y_true, predictions, is_daytime, model_names=None):
    """综合评估模型性能"""
    if model_names is None:
        model_names = list(predictions.keys())

    results = []

    # 只在白昼时段评估
    daytime_mask = is_daytime.astype(bool)

    for model_name in model_names:
        if model_name not in predictions:
            continue

        y_pred = predictions[model_name]

        # 白昼时段评估
        y_true_day = y_true[daytime_mask]
        y_pred_day = y_pred[daytime_mask]

        if len(y_true_day) == 0:
            continue

        # 计算评估指标
        mae = mean_absolute_error(y_true_day, y_pred_day)
        rmse = np.sqrt(mean_squared_error(y_true_day, y_pred_day))
        r2 = r2_score(y_true_day, y_pred_day)

        # nRMSE (归一化RMSE)
        nrmse = rmse / (y_true_day.max() - y_true_day.min()) if y_true_day.max() != y_true_day.min() else 0

        # SMAPE (对称平均绝对百分比误差)
        smape = np.mean(2 * np.abs(y_pred_day - y_true_day) / (np.abs(y_pred_day) + np.abs(y_true_day) + 1e-8)) * 100

        results.append({
            'Model': model_name,
            'MAE': mae,
            'RMSE': rmse,
            'nRMSE': nrmse,
            'SMAPE': smape,
            'R²': r2
        })

    return pd.DataFrame(results)

def analyze_nwp_contribution(models, X_test):
    """分析NWP特征的贡献度"""
    nwp_importance = {}

    for model_name, model in models.items():
        if hasattr(model, 'feature_importances_'):
            # 获取特征重要性
            feature_names = X_test.columns
            importances = model.feature_importances_

            # 分析NWP特征的重要性
            nwp_features = [i for i, name in enumerate(feature_names) if name.startswith('nwp_')]
            lmd_features = [i for i, name in enumerate(feature_names) if name.startswith('lmd_')]
            power_features = [i for i, name in enumerate(feature_names) if name.startswith('power_')]

            nwp_importance[model_name] = {
                'nwp_total': np.sum(importances[nwp_features]) if nwp_features else 0,
                'lmd_total': np.sum(importances[lmd_features]) if lmd_features else 0,
                'power_total': np.sum(importances[power_features]) if power_features else 0,
                'nwp_features': [(feature_names[i], importances[i]) for i in nwp_features],
                'top_nwp': sorted([(feature_names[i], importances[i]) for i in nwp_features],
                                key=lambda x: x[1], reverse=True)[:10]
            }

    return nwp_importance

def plot_nwp_comparison(results_df, save_path):
    """绘制NWP模型对比图"""
    plt.figure(figsize=(15, 10))

    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('NWP信息对光伏发电功率预测精度的影响分析', fontsize=16, fontweight='bold')

    # 1. MAE对比
    ax1 = axes[0, 0]
    models = results_df['Model']
    mae_values = results_df['MAE']
    colors = ['red' if 'Baseline' in model else 'blue' if 'NWP' in model else 'green' for model in models]
    bars1 = ax1.bar(range(len(models)), mae_values, color=colors, alpha=0.7)
    ax1.set_title('平均绝对误差 (MAE) 对比', fontweight='bold')
    ax1.set_ylabel('MAE (MW)')
    ax1.set_xticks(range(len(models)))
    ax1.set_xticklabels(models, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)

    # 添加数值标签
    for i, v in enumerate(mae_values):
        ax1.text(i, v + max(mae_values) * 0.01, f'{v:.3f}', ha='center', va='bottom')

    # 2. R²对比
    ax2 = axes[0, 1]
    r2_values = results_df['R²']
    bars2 = ax2.bar(range(len(models)), r2_values, color=colors, alpha=0.7)
    ax2.set_title('决定系数 (R²) 对比', fontweight='bold')
    ax2.set_ylabel('R²')
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels(models, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)

    for i, v in enumerate(r2_values):
        ax2.text(i, v + max(r2_values) * 0.01, f'{v:.3f}', ha='center', va='bottom')

    # 3. RMSE对比
    ax3 = axes[0, 2]
    rmse_values = results_df['RMSE']
    bars3 = ax3.bar(range(len(models)), rmse_values, color=colors, alpha=0.7)
    ax3.set_title('均方根误差 (RMSE) 对比', fontweight='bold')
    ax3.set_ylabel('RMSE (MW)')
    ax3.set_xticks(range(len(models)))
    ax3.set_xticklabels(models, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)

    for i, v in enumerate(rmse_values):
        ax3.text(i, v + max(rmse_values) * 0.01, f'{v:.3f}', ha='center', va='bottom')

    # 4. 改进百分比分析
    ax4 = axes[1, 0]
    baseline_mae = results_df[results_df['Model'].str.contains('Baseline')]['MAE'].iloc[0]
    improvements = [(baseline_mae - mae) / baseline_mae * 100 for mae in mae_values]
    bars4 = ax4.bar(range(len(models)), improvements, color=colors, alpha=0.7)
    ax4.set_title('相对基准模型的MAE改进率 (%)', fontweight='bold')
    ax4.set_ylabel('改进率 (%)')
    ax4.set_xticks(range(len(models)))
    ax4.set_xticklabels(models, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    for i, v in enumerate(improvements):
        ax4.text(i, v + max(improvements) * 0.01, f'{v:.1f}%', ha='center', va='bottom')

    # 5. 综合性能雷达图
    ax5 = axes[1, 1]
    # 归一化指标（越大越好）
    normalized_metrics = results_df.copy()
    normalized_metrics['MAE_norm'] = 1 - (normalized_metrics['MAE'] - normalized_metrics['MAE'].min()) / (normalized_metrics['MAE'].max() - normalized_metrics['MAE'].min())
    normalized_metrics['RMSE_norm'] = 1 - (normalized_metrics['RMSE'] - normalized_metrics['RMSE'].min()) / (normalized_metrics['RMSE'].max() - normalized_metrics['RMSE'].min())
    normalized_metrics['R²_norm'] = normalized_metrics['R²']

    # 选择几个代表性模型绘制雷达图
    selected_models = ['LightGBM_Baseline', 'LightGBM_NWP', 'LightGBM_Downscaled']
    selected_data = normalized_metrics[normalized_metrics['Model'].isin(selected_models)]

    angles = np.linspace(0, 2 * np.pi, 3, endpoint=False).tolist()
    angles += angles[:1]  # 闭合

    ax5 = plt.subplot(2, 3, 5, projection='polar')
    for i, (_, row) in enumerate(selected_data.iterrows()):
        values = [row['MAE_norm'], row['RMSE_norm'], row['R²_norm']]
        values += values[:1]
        ax5.plot(angles, values, 'o-', linewidth=2, label=row['Model'])
        ax5.fill(angles, values, alpha=0.25)

    ax5.set_xticks(angles[:-1])
    ax5.set_xticklabels(['MAE', 'RMSE', 'R²'])
    ax5.set_title('综合性能对比 (雷达图)', fontweight='bold')
    ax5.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    # 6. 模型类型分组对比
    ax6 = axes[1, 2]
    model_types = ['Baseline', 'NWP', 'Downscaled']
    type_mae = []
    type_r2 = []

    for model_type in model_types:
        type_models = results_df[results_df['Model'].str.contains(model_type)]
        if len(type_models) > 0:
            type_mae.append(type_models['MAE'].mean())
            type_r2.append(type_models['R²'].mean())
        else:
            type_mae.append(0)
            type_r2.append(0)

    x = np.arange(len(model_types))
    width = 0.35

    bars1 = ax6.bar(x - width/2, type_mae, width, label='MAE', alpha=0.7, color='red')
    ax6_twin = ax6.twinx()
    bars2 = ax6_twin.bar(x + width/2, type_r2, width, label='R²', alpha=0.7, color='blue')

    ax6.set_xlabel('模型类型')
    ax6.set_ylabel('MAE (MW)', color='red')
    ax6_twin.set_ylabel('R²', color='blue')
    ax6.set_title('不同模型类型的平均性能', fontweight='bold')
    ax6.set_xticks(x)
    ax6.set_xticklabels(model_types)

    # 添加图例
    lines1, labels1 = ax6.get_legend_handles_labels()
    lines2, labels2 = ax6_twin.get_legend_handles_labels()
    ax6.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def plot_feature_importance_analysis(nwp_importance, save_path):
    """绘制NWP特征重要性分析图"""
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('NWP特征重要性分析', fontsize=16, fontweight='bold')

    # 1. 特征类型重要性对比
    ax1 = axes[0, 0]
    models = list(nwp_importance.keys())
    nwp_totals = [nwp_importance[model]['nwp_total'] for model in models]
    lmd_totals = [nwp_importance[model]['lmd_total'] for model in models]
    power_totals = [nwp_importance[model]['power_total'] for model in models]

    x = np.arange(len(models))
    width = 0.25

    ax1.bar(x - width, nwp_totals, width, label='NWP特征', alpha=0.8, color='blue')
    ax1.bar(x, lmd_totals, width, label='LMD特征', alpha=0.8, color='green')
    ax1.bar(x + width, power_totals, width, label='功率历史特征', alpha=0.8, color='orange')

    ax1.set_xlabel('模型')
    ax1.set_ylabel('特征重要性总和')
    ax1.set_title('不同类型特征的重要性对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels(models, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. Top NWP特征重要性
    ax2 = axes[0, 1]
    # 选择一个代表性模型显示top特征
    if 'LightGBM_NWP' in nwp_importance:
        top_features = nwp_importance['LightGBM_NWP']['top_nwp'][:10]
        feature_names = [f[0] for f in top_features]
        feature_importances = [f[1] for f in top_features]

        bars = ax2.barh(range(len(feature_names)), feature_importances, alpha=0.8, color='skyblue')
        ax2.set_yticks(range(len(feature_names)))
        ax2.set_yticklabels(feature_names)
        ax2.set_xlabel('特征重要性')
        ax2.set_title('Top 10 NWP特征重要性 (LightGBM_NWP)')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, v in enumerate(feature_importances):
            ax2.text(v + max(feature_importances) * 0.01, i, f'{v:.3f}', va='center')

    # 3. NWP vs LMD特征对比
    ax3 = axes[1, 0]
    nwp_ratios = [nwp_importance[model]['nwp_total'] /
                 (nwp_importance[model]['nwp_total'] + nwp_importance[model]['lmd_total'] + 1e-8)
                 for model in models]
    lmd_ratios = [nwp_importance[model]['lmd_total'] /
                 (nwp_importance[model]['nwp_total'] + nwp_importance[model]['lmd_total'] + 1e-8)
                 for model in models]

    ax3.bar(x, nwp_ratios, width*2, label='NWP特征占比', alpha=0.8, color='blue')
    ax3.bar(x, lmd_ratios, width*2, bottom=nwp_ratios, label='LMD特征占比', alpha=0.8, color='green')

    ax3.set_xlabel('模型')
    ax3.set_ylabel('特征重要性占比')
    ax3.set_title('NWP vs LMD特征重要性占比')
    ax3.set_xticks(x)
    ax3.set_xticklabels(models, rotation=45, ha='right')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 特征重要性热力图
    ax4 = axes[1, 1]
    # 构建特征重要性矩阵
    all_nwp_features = set()
    for model in models:
        for feature, _ in nwp_importance[model]['nwp_features']:
            all_nwp_features.add(feature)

    all_nwp_features = sorted(list(all_nwp_features))[:15]  # 取前15个特征
    importance_matrix = []

    for model in models:
        model_importances = []
        feature_dict = dict(nwp_importance[model]['nwp_features'])
        for feature in all_nwp_features:
            model_importances.append(feature_dict.get(feature, 0))
        importance_matrix.append(model_importances)

    im = ax4.imshow(importance_matrix, cmap='YlOrRd', aspect='auto')
    ax4.set_xticks(range(len(all_nwp_features)))
    ax4.set_xticklabels(all_nwp_features, rotation=45, ha='right')
    ax4.set_yticks(range(len(models)))
    ax4.set_yticklabels(models)
    ax4.set_title('NWP特征重要性热力图')

    # 添加颜色条
    plt.colorbar(im, ax=ax4, shrink=0.8)

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def save_predictions_table_q3q4(test_dates, test_times, y_true, predictions, is_daytime, save_path):
    """保存预测结果为表2格式"""
    # 创建结果表格
    results_table = pd.DataFrame({
        'date_time': test_times,
        'actual_power': y_true,
        'is_daytime': is_daytime
    })

    # 添加各模型的预测结果
    for model_name, y_pred in predictions.items():
        results_table[f'{model_name}_prediction'] = y_pred
        # 计算误差
        results_table[f'{model_name}_error'] = y_pred - y_true
        results_table[f'{model_name}_abs_error'] = np.abs(y_pred - y_true)

    # 只保存白昼时段的结果
    daytime_results = results_table[results_table['is_daytime'] == 1].copy()

    # 保存完整结果
    results_table.to_csv(save_path.replace('.csv', '_full.csv'), index=False)

    # 保存白昼时段结果
    daytime_results.to_csv(save_path, index=False)

    print(f"预测结果已保存到: {save_path}")
    print(f"完整结果已保存到: {save_path.replace('.csv', '_full.csv')}")

    return results_table, daytime_results

def main():
    """主函数：执行问题3和问题4的完整分析"""
    print("=" * 80)
    print("问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析")
    print("=" * 80)

    start_time = time.time()

    # 选择站点（使用站点1作为示例）
    station_id = 1
    print(f"\n=== 加载站点{station_id}数据 ===")

    # 1. 加载数据
    df = load_station_data(station_id)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['date_time'].min()} 到 {df['date_time'].max()}")

    # 2. 特征工程
    print("\n=== 进行NWP特征工程 ===")
    df_features = prepare_nwp_features(df)
    print(f"特征工程后形状: {df_features.shape}")

    # 检查NWP特征
    nwp_cols = [col for col in df_features.columns if col.startswith('nwp_')]
    print(f"NWP特征数量: {len(nwp_cols)}")
    print(f"前10个NWP特征: {nwp_cols[:10]}")

    # 3. 空间降尺度处理
    print("\n=== 进行NWP空间降尺度处理 ===")
    df_downscaled = spatial_downscaling_nwp(df_features, method='kriging')
    downscaled_cols = [col for col in df_downscaled.columns if 'downscaled' in col]
    print(f"降尺度特征数量: {len(downscaled_cols)}")
    print(f"降尺度特征: {downscaled_cols}")

    # 4. 数据划分
    print("\n=== 划分训练集和测试集 ===")
    train_indices, test_indices = get_train_test_split_q3q4(df_downscaled)
    print(f"训练集大小: {len(train_indices)}")
    print(f"测试集大小: {len(test_indices)}")

    # 准备特征和目标变量
    feature_cols = [col for col in df_downscaled.columns
                   if col not in ['power', 'date_time', 'date']]

    X = df_downscaled[feature_cols]
    y = df_downscaled['power']

    X_train = X.loc[train_indices]
    X_test = X.loc[test_indices]
    y_train = y.loc[train_indices]
    y_test = y.loc[test_indices]

    # 获取测试集的时间信息
    test_times = df_downscaled.loc[test_indices, 'date_time']
    test_is_daytime = df_downscaled.loc[test_indices, 'is_daytime']

    print(f"特征数量: {len(feature_cols)}")
    print(f"训练集特征形状: {X_train.shape}")
    print(f"测试集特征形状: {X_test.shape}")

    # 5. 构建和训练模型
    print("\n=== 构建融入NWP信息的预测模型 ===")
    nwp_models, nwp_predictions = build_nwp_enhanced_models(X_train, y_train, X_test)

    print("\n=== 构建使用降尺度NWP数据的模型 ===")
    downscaled_models, downscaled_predictions = build_downscaled_models(X_train, y_train, X_test)

    # 合并所有模型和预测结果
    all_models = {**nwp_models, **downscaled_models}
    all_predictions = {**nwp_predictions, **downscaled_predictions}

    print(f"\n总共训练了 {len(all_models)} 个模型")
    print(f"模型列表: {list(all_models.keys())}")

    # 6. 模型评估
    print("\n=== 模型性能评估 ===")
    results_df = evaluate_models_comprehensive(y_test.values, all_predictions, test_is_daytime.values)
    print("\n模型性能对比:")
    print(results_df.round(4))

    # 保存评估结果
    results_df.to_csv(os.path.join(RESULTS_DIR, 'model_performance_comparison.csv'), index=False)

    # 7. NWP特征重要性分析
    print("\n=== NWP特征重要性分析 ===")
    nwp_importance = analyze_nwp_contribution(all_models, X_test)

    # 保存特征重要性分析
    importance_summary = []
    for model_name, importance_data in nwp_importance.items():
        importance_summary.append({
            'Model': model_name,
            'NWP_Total_Importance': importance_data['nwp_total'],
            'LMD_Total_Importance': importance_data['lmd_total'],
            'Power_Total_Importance': importance_data['power_total'],
            'NWP_Ratio': importance_data['nwp_total'] / (importance_data['nwp_total'] + importance_data['lmd_total'] + 1e-8)
        })

    importance_df = pd.DataFrame(importance_summary)
    importance_df.to_csv(os.path.join(RESULTS_DIR, 'nwp_feature_importance_summary.csv'), index=False)

    # 8. 生成可视化图表
    print("\n=== 生成可视化图表 ===")

    # NWP模型对比图
    plot_nwp_comparison(results_df, os.path.join(RESULTS_DIR, 'nwp_model_comparison.png'))
    print("✓ NWP模型对比图已保存")

    # 特征重要性分析图
    plot_feature_importance_analysis(nwp_importance, os.path.join(RESULTS_DIR, 'nwp_feature_importance_analysis.png'))
    print("✓ NWP特征重要性分析图已保存")

    # 9. 保存预测结果表格
    print("\n=== 保存预测结果表格 ===")
    results_table, daytime_results = save_predictions_table_q3q4(
        None, test_times.values, y_test.values, all_predictions,
        test_is_daytime.values, os.path.join(RESULTS_DIR, 'prediction_results_table2.csv')
    )

    # 10. 分析结论
    print("\n" + "=" * 80)
    print("分析结论")
    print("=" * 80)

    # 找出最佳模型
    best_model = results_df.loc[results_df['R²'].idxmax()]
    baseline_model = results_df[results_df['Model'].str.contains('Baseline')].iloc[0]

    print(f"\n【问题3：融入NWP信息的效果分析】")
    print(f"最佳模型: {best_model['Model']}")
    print(f"最佳模型性能: MAE={best_model['MAE']:.4f}, R²={best_model['R²']:.4f}")
    print(f"基准模型性能: MAE={baseline_model['MAE']:.4f}, R²={baseline_model['R²']:.4f}")

    mae_improvement = (baseline_model['MAE'] - best_model['MAE']) / baseline_model['MAE'] * 100
    r2_improvement = (best_model['R²'] - baseline_model['R²']) / baseline_model['R²'] * 100

    print(f"MAE改进: {mae_improvement:.2f}%")
    print(f"R²改进: {r2_improvement:.2f}%")

    if mae_improvement > 5:
        print("✓ 融入NWP信息能够有效提高预测精度")
    else:
        print("✗ 融入NWP信息对预测精度提升有限")

    # 分析降尺度效果
    downscaled_models_df = results_df[results_df['Model'].str.contains('Downscaled')]
    nwp_models_df = results_df[results_df['Model'].str.contains('NWP') & ~results_df['Model'].str.contains('Downscaled')]

    if len(downscaled_models_df) > 0 and len(nwp_models_df) > 0:
        print(f"\n【问题4：NWP空间降尺度效果分析】")
        avg_downscaled_mae = downscaled_models_df['MAE'].mean()
        avg_nwp_mae = nwp_models_df['MAE'].mean()
        downscale_improvement = (avg_nwp_mae - avg_downscaled_mae) / avg_nwp_mae * 100

        print(f"原始NWP模型平均MAE: {avg_nwp_mae:.4f}")
        print(f"降尺度NWP模型平均MAE: {avg_downscaled_mae:.4f}")
        print(f"降尺度改进: {downscale_improvement:.2f}%")

        if downscale_improvement > 2:
            print("✓ NWP空间降尺度能够提高预测精度")
        else:
            print("✗ NWP空间降尺度对预测精度提升有限")

    # 特征重要性分析
    print(f"\n【NWP特征重要性分析】")
    avg_nwp_ratio = importance_df['NWP_Ratio'].mean()
    print(f"NWP特征平均重要性占比: {avg_nwp_ratio:.2f}")

    if avg_nwp_ratio > 0.3:
        print("✓ NWP特征在预测中起重要作用")
    else:
        print("✗ NWP特征重要性相对较低")

    # 运行时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n总运行时间: {total_time:.2f}秒")

    print(f"\n所有结果已保存到目录: {RESULTS_DIR}")
    print("=" * 80)

if __name__ == "__main__":
    main()
