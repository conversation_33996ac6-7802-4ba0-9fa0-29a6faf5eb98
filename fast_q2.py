import os
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from sklearn.metrics import mean_absolute_error, r2_score
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, ExtraTreesRegressor, GradientBoostingRegressor
from sklearn.tree import DecisionTreeRegressor
from sklearn.neighbors import KNeighborsRegressor
from sklearn.svm import SVR
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import cross_val_score
from tqdm import tqdm
import pickle
import time
import lightgbm as lgb
from xgboost import XGBRegressor
import catboost as cb
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader

# 设置中文字体和图表风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建结果目录
RESULTS_DIR = 'Q2_fast_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 4090优化设置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {DEVICE}")

# 针对4090的优化参数
if torch.cuda.is_available():
    # 启用混合精度训练
    torch.backends.cudnn.benchmark = True
    torch.backends.cudnn.deterministic = False
    # 设置更大的批次大小以充分利用4090的显存
    BATCH_SIZE = 1024  # 4090有24GB显存，可以设置更大
    # 增加工作线程数
    NUM_WORKERS = 16   # 4090配合高端CPU可以设置更多线程
    # 启用内存映射
    PIN_MEMORY = True
else:
    BATCH_SIZE = 64
    NUM_WORKERS = 4
    PIN_MEMORY = False

print(f"批次大小: {BATCH_SIZE}, 工作线程数: {NUM_WORKERS}")

# 快速模型配置
FAST_CONFIG = {
    'lgbm_rounds': 300,      # LightGBM训练轮数
    'xgb_estimators': 300,   # XGBoost估计器数量
    'catboost_iterations': 300,  # CatBoost迭代次数
    'rf_estimators': 100,    # RandomForest估计器数量
    'nn_epochs': 50,         # 神经网络训练轮数
    'cv_folds': 3,           # 交叉验证折数
}

# 数据处理函数
def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])

    # 添加时间相关特征
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['day'] = df['date_time'].dt.day
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek
    df['date'] = df['date_time'].dt.date

    # 添加时间段标记（96个时间点）
    df['time_point'] = df['hour'] * 4 + df['minute'] // 15

    # 添加白昼标记 (6点到18点之间视为白昼)
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] <= 18)).astype(int)

    return df

def prepare_features(df, lag_days=[1, 2, 3], window_sizes=[12, 24, 96]):
    """准备预测特征 - 简化版"""
    # 复制数据，避免修改原始数据
    df_features = df.copy()

    # 1. 滞后功率特征
    for day in lag_days:
        lag_points = day * 96  # 每天96个时间点
        df_features[f'power_lag_{day}d'] = df_features['power'].shift(lag_points)

    # 2. 滑动统计特征
    for window in window_sizes:
        # 滑动平均
        df_features[f'power_mean_{window}'] = df_features['power'].rolling(window=window, min_periods=1).mean()
        # 滑动标准差
        df_features[f'power_std_{window}'] = df_features['power'].rolling(window=window, min_periods=1).std()

    # 3. 时间特征的周期性表示
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)

    # 4. 使用NWP气象数据特征
    for col in df.columns:
        if 'nwp_' in col:
            # 添加滞后特征
            df_features[f'{col}_lag_1d'] = df_features[col].shift(96)

    # 5. 填充缺失值，使用前后值的中位数
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def get_train_test_split(df, test_months=[2, 5, 8, 11]):
    """按照题目要求划分训练集和测试集

    训练集：除2/5/8/11月最后一周外的数据
    测试集：2/5/8/11月最后一周数据
    """
    # 先按月份筛选出测试月份
    test_month_df = df[df['month'].isin(test_months)]

    # 对于每个测试月份，找出最后一周
    test_indices = []
    test_month_data = {}

    for month in test_months:
        month_df = df[df['month'] == month]
        if len(month_df) == 0:
            continue

        # 获取该月最大日期
        max_date = month_df['date'].max()

        # 获取最大日期前7天的数据作为测试集
        test_dates = [(max_date - timedelta(days=i)) for i in range(7)]
        month_test_indices = df[df['date'].isin(test_dates)].index
        test_indices.extend(month_test_indices)

        # 保存每个月的测试数据用于分别绘图
        test_month_data[month] = df[df['date'].isin(test_dates)].copy()

    # 剩余数据作为训练集
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices, test_month_data

def evaluate_models_enhanced(y_true, predictions, is_daytime=None):
    """增强的模型评估函数，包含R²指标"""
    results = {}

    # 如果提供了白昼标记，只在白昼时段计算指标
    if is_daytime is not None:
        daytime_mask = is_daytime.astype(bool)
        y_true_eval = y_true[daytime_mask]
    else:
        y_true_eval = y_true
        daytime_mask = np.ones(len(y_true), dtype=bool)

    for model_name, y_pred in predictions.items():
        if is_daytime is not None:
            y_pred_eval = y_pred[daytime_mask]
        else:
            y_pred_eval = y_pred

        # 计算平均绝对误差 (MAE)
        mae = np.mean(np.abs(y_true_eval - y_pred_eval))

        # 计算均方根误差 (RMSE)
        rmse = np.sqrt(np.mean((y_true_eval - y_pred_eval) ** 2))

        # 计算归一化均方根误差 (nRMSE)
        nrmse = rmse / np.mean(y_true_eval) if np.mean(y_true_eval) > 0 else 0

        # 计算对称平均绝对百分比误差 (SMAPE)
        smape = 100 * np.mean(2 * np.abs(y_pred_eval - y_true_eval) / (np.abs(y_pred_eval) + np.abs(y_true_eval) + 1e-8))

        # 计算R²决定系数
        r2 = r2_score(y_true_eval, y_pred_eval)

        results[model_name] = {
            'MAE': mae,
            'RMSE': rmse,
            'nRMSE': nrmse,
            'SMAPE': smape,
            'R²': r2
        }

    # 转换为DataFrame以便于展示
    results_df = pd.DataFrame(results).T
    return results_df

def persistence_model(train_data, test_dates, time_points=96):
    """
    实现Persistence模型（基准线）
    使用前一天同一时刻的功率作为预测值
    """
    print("运行Persistence基准模型...")
    predictions = []

    # 对每个测试日期进行预测
    for test_date in test_dates:
        # 获取前一天的日期
        prev_day = test_date - timedelta(days=1)

        # 获取前一天的功率值
        prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values

        if len(prev_day_powers) == time_points:
            # 如果前一天有完整的数据，直接使用
            predictions.extend(prev_day_powers)
        else:
            # 如果前一天数据不完整，使用最近的有效日期
            for day_offset in range(2, 8):  # 尝试前2~7天
                prev_day = test_date - timedelta(days=day_offset)
                prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
                if len(prev_day_powers) == time_points:
                    predictions.extend(prev_day_powers)
                    break
            else:
                # 如果前7天都没有完整数据，使用训练集中同时刻的平均值
                for time_point in range(time_points):
                    time_point_avg = train_data[train_data['time_point'] == time_point]['power'].mean()
                    predictions.append(time_point_avg)

    return np.array(predictions)

def fast_lightgbm_model(X_train, y_train, X_test):
    """
    使用LightGBM模型进行快速预测 - 简化版本
    """
    print("运行快速LightGBM模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_lgbm = X_train[feature_cols].copy()
    X_test_lgbm = X_test[feature_cols].copy()

    # LightGBM参数 - 简化版，提高训练速度
    params = {
        'boosting_type': 'gbdt',
        'objective': 'regression',
        'metric': 'mae',
        'learning_rate': 0.1,
        'num_leaves': 31,
        'max_depth': 6,
        'feature_fraction': 0.8,
        'bagging_fraction': 0.8,
        'bagging_freq': 5,
        'verbose': -1
    }

    # 创建数据集
    train_data = lgb.Dataset(X_train_lgbm, label=y_train)

    # 训练模型 - 减少迭代次数，提高速度
    model = lgb.train(
        params,
        train_data,
        num_boost_round=200  # 减少迭代次数
    )

    # 预测
    predictions = model.predict(X_test_lgbm)

    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.feature_importance()
    }).sort_values(by='Importance', ascending=False)

    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'lightgbm_feature_importance.csv'), index=False)

    # 可视化前15个重要特征
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feature_importance.head(15))
    plt.title('LightGBM 特征重要性 (Top 15)')
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'lightgbm_feature_importance.png'))
    plt.close()

    return predictions, feature_importance

def fast_catboost_model(X_train, y_train, X_test):
    """
    使用CatBoost模型进行快速预测
    """
    print("运行快速CatBoost模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_cb = X_train[feature_cols].copy()
    X_test_cb = X_test[feature_cols].copy()

    # CatBoost参数 - 快速版本
    model = cb.CatBoostRegressor(
        iterations=FAST_CONFIG['catboost_iterations'],
        learning_rate=0.1,
        depth=6,
        l2_leaf_reg=3,
        verbose=False,
        thread_count=NUM_WORKERS
    )

    # 训练模型
    model.fit(X_train_cb, y_train)

    # 预测
    predictions = model.predict(X_test_cb)

    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.get_feature_importance()
    }).sort_values(by='Importance', ascending=False)

    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'catboost_feature_importance.csv'), index=False)

    return predictions, feature_importance

def fast_random_forest_model(X_train, y_train, X_test):
    """
    使用随机森林模型进行快速预测
    """
    print("运行快速随机森林模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_rf = X_train[feature_cols].copy()
    X_test_rf = X_test[feature_cols].copy()

    # 随机森林参数 - 快速版本
    model = RandomForestRegressor(
        n_estimators=FAST_CONFIG['rf_estimators'],
        max_depth=10,
        min_samples_split=5,
        min_samples_leaf=2,
        n_jobs=NUM_WORKERS,
        random_state=42
    )

    # 训练模型
    model.fit(X_train_rf, y_train)

    # 预测
    predictions = model.predict(X_test_rf)

    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.feature_importances_
    }).sort_values(by='Importance', ascending=False)

    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'random_forest_feature_importance.csv'), index=False)

    return predictions, feature_importance

def fast_linear_models(X_train, y_train, X_test):
    """
    使用多种线性模型进行快速预测
    """
    print("运行快速线性模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_linear = X_train[feature_cols].copy()
    X_test_linear = X_test[feature_cols].copy()

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_linear)
    X_test_scaled = scaler.transform(X_test_linear)

    models = {
        'Ridge': Ridge(alpha=1.0),
        'Lasso': Lasso(alpha=0.1),
        'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5)
    }

    predictions = {}

    for name, model in models.items():
        # 训练模型
        model.fit(X_train_scaled, y_train)

        # 预测
        pred = model.predict(X_test_scaled)
        predictions[name] = pred

    return predictions

def fast_ensemble_models(X_train, y_train, X_test):
    """
    使用集成模型进行快速预测
    """
    print("运行快速集成模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_ens = X_train[feature_cols].copy()
    X_test_ens = X_test[feature_cols].copy()

    models = {
        'ExtraTrees': ExtraTreesRegressor(
            n_estimators=100,
            max_depth=10,
            n_jobs=NUM_WORKERS,
            random_state=42
        ),
        'GradientBoosting': GradientBoostingRegressor(
            n_estimators=100,
            max_depth=6,
            learning_rate=0.1,
            random_state=42
        )
    }

    predictions = {}

    for name, model in models.items():
        # 训练模型
        model.fit(X_train_ens, y_train)

        # 预测
        pred = model.predict(X_test_ens)
        predictions[name] = pred

    return predictions

# 简单的神经网络模型
class FastNN(nn.Module):
    def __init__(self, input_size):
        super(FastNN, self).__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

    def forward(self, x):
        return self.layers(x)

def fast_neural_network_model(X_train, y_train, X_test):
    """
    使用简单神经网络进行快速预测
    """
    print("运行快速神经网络模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_nn = X_train[feature_cols].copy()
    X_test_nn = X_test[feature_cols].copy()

    # 标准化特征
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_nn)
    X_test_scaled = scaler.transform(X_test_nn)

    # 转换为张量
    X_train_tensor = torch.FloatTensor(X_train_scaled).to(DEVICE)
    y_train_tensor = torch.FloatTensor(y_train.reshape(-1, 1)).to(DEVICE)
    X_test_tensor = torch.FloatTensor(X_test_scaled).to(DEVICE)

    # 创建模型
    model = FastNN(X_train_scaled.shape[1]).to(DEVICE)
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 训练模型
    model.train()
    for epoch in range(FAST_CONFIG['nn_epochs']):
        optimizer.zero_grad()
        outputs = model(X_train_tensor)
        loss = criterion(outputs, y_train_tensor)
        loss.backward()
        optimizer.step()

    # 预测
    model.eval()
    with torch.no_grad():
        predictions = model(X_test_tensor).cpu().numpy().flatten()

    return predictions

def fast_xgboost_model(X_train, y_train, X_test):
    """
    使用XGBoost模型进行快速预测 - 简化版本
    """
    print("运行快速XGBoost模型...")

    # 去掉非特征列
    drop_cols = ['date_time', 'date', 'time_point']
    feature_cols = [col for col in X_train.columns if col not in drop_cols and col != 'power']

    X_train_xgb = X_train[feature_cols].copy()
    X_test_xgb = X_test[feature_cols].copy()

    # 训练模型 - 使用更快的参数设置
    model = XGBRegressor(
        n_estimators=200,  # 减少树的数量
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        objective='reg:squarederror',
        n_jobs=-1  # 使用全部CPU核心
    )

    # 训练模型
    model.fit(X_train_xgb, y_train)

    # 预测
    predictions = model.predict(X_test_xgb)

    # 获取特征重要性
    feature_importance = pd.DataFrame({
        'Feature': feature_cols,
        'Importance': model.feature_importances_
    }).sort_values(by='Importance', ascending=False)

    # 保存特征重要性
    feature_importance.to_csv(os.path.join(RESULTS_DIR, 'xgboost_feature_importance.csv'), index=False)

    # 可视化前15个重要特征
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feature_importance.head(15))
    plt.title('XGBoost 特征重要性 (Top 15)')
    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'xgboost_feature_importance.png'))
    plt.close()

    return predictions, feature_importance

def plot_prediction_comparison(y_true, predictions, is_daytime, title, save_path):
    """绘制不同模型的预测效果对比"""
    # 将数据和预测转为DataFrame以便处理
    result_df = pd.DataFrame({'True': y_true})

    # 添加预测值和白昼标记
    for model_name, y_pred in predictions.items():
        result_df[model_name] = y_pred

    result_df['is_daytime'] = is_daytime

    # 绘制全部数据对比
    plt.figure(figsize=(15, 6))
    plt.plot(result_df['True'].values, 'k-', label='真实值', linewidth=2)

    # 绘制不同模型的预测值
    for model_name in predictions.keys():
        plt.plot(result_df[model_name].values, '--', label=f'{model_name}预测值', linewidth=1.5, alpha=0.8)

    # 标注白天区域
    for i in range(len(result_df)):
        if i > 0 and result_df['is_daytime'].iloc[i] != result_df['is_daytime'].iloc[i-1]:
            if result_df['is_daytime'].iloc[i] == 1:
                plt.axvline(x=i, color='gold', linestyle='-', alpha=0.3)
                plt.text(i, result_df['True'].max() * 0.9, "白昼开始", rotation=90, alpha=0.7)
            else:
                plt.axvline(x=i, color='navy', linestyle='-', alpha=0.3)
                plt.text(i, result_df['True'].max() * 0.9, "白昼结束", rotation=90, alpha=0.7)

    plt.title(title, fontsize=14)
    plt.xlabel('时间点', fontsize=12)
    plt.ylabel('功率 (MW)', fontsize=12)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    plt.savefig(save_path)
    plt.close()

    # 只绘制前两天的数据，以便更清晰地查看
    if len(result_df) > 96*2:
        plt.figure(figsize=(15, 6))

        # 提取前两天数据
        first_two_days = result_df.iloc[:96*2]
        plt.plot(first_two_days['True'].values, 'k-', label='真实值', linewidth=2)

        for model_name in predictions.keys():
            plt.plot(first_two_days[model_name].values, '--', label=f'{model_name}预测值', linewidth=1.5, alpha=0.8)

        # 标注白天区域
        for i in range(len(first_two_days)):
            if i > 0 and first_two_days['is_daytime'].iloc[i] != first_two_days['is_daytime'].iloc[i-1]:
                if first_two_days['is_daytime'].iloc[i] == 1:
                    plt.axvline(x=i, color='gold', linestyle='-', alpha=0.3)
                    plt.text(i, first_two_days['True'].max() * 0.9, "白昼开始", rotation=90, alpha=0.7)
                else:
                    plt.axvline(x=i, color='navy', linestyle='-', alpha=0.3)
                    plt.text(i, first_two_days['True'].max() * 0.9, "白昼结束", rotation=90, alpha=0.7)

        plt.title(f"{title} (前两天)", fontsize=14)
        plt.xlabel('时间点', fontsize=12)
        plt.ylabel('功率 (MW)', fontsize=12)
        plt.legend(loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()

        plt.savefig(save_path.replace('.png', '_2days.png'))
        plt.close()

def save_predictions_to_table(test_dates, test_times, y_true, predictions, is_daytime):
    """将预测结果保存为表2格式"""
    # 创建结果DataFrame
    result_table = pd.DataFrame({
        'date_time': test_times,
        'actual_power': y_true
    })

    # 添加预测值
    for model_name, y_pred in predictions.items():
        result_table[f'{model_name}_power'] = y_pred

    # 添加白天标记
    result_table['is_daytime'] = is_daytime

    # 保存表格
    result_table.to_csv(os.path.join(RESULTS_DIR, 'prediction_results_table2.csv'), index=False)

    # 另存一份只包含白天时段的结果
    result_table[result_table['is_daytime'] == 1].to_csv(
        os.path.join(RESULTS_DIR, 'prediction_results_daytime_only.csv'), index=False)

    return result_table

def plot_enhanced_visualizations(y_true, predictions, is_daytime, results_df):
    """绘制增强的可视化图表"""

    # 1. 模型性能对比柱状图
    plt.figure(figsize=(15, 10))

    # 子图1: MAE对比
    plt.subplot(2, 3, 1)
    mae_data = results_df['MAE'].sort_values()
    colors = plt.cm.viridis(np.linspace(0, 1, len(mae_data)))
    bars = plt.bar(range(len(mae_data)), mae_data.values, color=colors)
    plt.xticks(range(len(mae_data)), mae_data.index, rotation=45, ha='right')
    plt.title('平均绝对误差 (MAE)', fontsize=12)
    plt.ylabel('MAE (MW)')
    plt.grid(True, alpha=0.3)

    # 子图2: RMSE对比
    plt.subplot(2, 3, 2)
    rmse_data = results_df['RMSE'].sort_values()
    colors = plt.cm.plasma(np.linspace(0, 1, len(rmse_data)))
    plt.bar(range(len(rmse_data)), rmse_data.values, color=colors)
    plt.xticks(range(len(rmse_data)), rmse_data.index, rotation=45, ha='right')
    plt.title('均方根误差 (RMSE)', fontsize=12)
    plt.ylabel('RMSE (MW)')
    plt.grid(True, alpha=0.3)

    # 子图3: R²对比
    plt.subplot(2, 3, 3)
    r2_data = results_df['R²'].sort_values(ascending=False)
    colors = plt.cm.coolwarm(np.linspace(0, 1, len(r2_data)))
    plt.bar(range(len(r2_data)), r2_data.values, color=colors)
    plt.xticks(range(len(r2_data)), r2_data.index, rotation=45, ha='right')
    plt.title('决定系数 (R²)', fontsize=12)
    plt.ylabel('R²')
    plt.grid(True, alpha=0.3)

    # 子图4: nRMSE对比
    plt.subplot(2, 3, 4)
    nrmse_data = results_df['nRMSE'].sort_values()
    colors = plt.cm.inferno(np.linspace(0, 1, len(nrmse_data)))
    plt.bar(range(len(nrmse_data)), nrmse_data.values, color=colors)
    plt.xticks(range(len(nrmse_data)), nrmse_data.index, rotation=45, ha='right')
    plt.title('归一化均方根误差 (nRMSE)', fontsize=12)
    plt.ylabel('nRMSE')
    plt.grid(True, alpha=0.3)

    # 子图5: SMAPE对比
    plt.subplot(2, 3, 5)
    smape_data = results_df['SMAPE'].sort_values()
    colors = plt.cm.magma(np.linspace(0, 1, len(smape_data)))
    plt.bar(range(len(smape_data)), smape_data.values, color=colors)
    plt.xticks(range(len(smape_data)), smape_data.index, rotation=45, ha='right')
    plt.title('对称平均绝对百分比误差 (SMAPE)', fontsize=12)
    plt.ylabel('SMAPE (%)')
    plt.grid(True, alpha=0.3)

    # 子图6: 综合排名热力图
    plt.subplot(2, 3, 6)
    ranking_data = pd.DataFrame()
    for metric in ['MAE', 'RMSE', 'nRMSE', 'SMAPE']:
        ranking_data[metric] = results_df[metric].rank()
    ranking_data['R²'] = results_df['R²'].rank(ascending=False)

    sns.heatmap(ranking_data.T, annot=True, cmap='RdYlGn_r', fmt='.0f',
                cbar_kws={'label': '排名 (越小越好)'})
    plt.title('模型综合排名热力图', fontsize=12)
    plt.xlabel('模型')
    plt.ylabel('评估指标')

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'enhanced_model_comparison.png'))
    plt.close()

    # 2. 预测效果散点图矩阵
    n_models = min(6, len(predictions))
    model_list = list(predictions.keys())[:n_models]

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    for i, model_name in enumerate(model_list):
        y_pred = predictions[model_name]

        # 只显示白昼时段的数据
        if is_daytime is not None:
            mask = is_daytime.astype(bool)
            y_true_plot = y_true[mask]
            y_pred_plot = y_pred[mask]
        else:
            y_true_plot = y_true
            y_pred_plot = y_pred

        axes[i].scatter(y_true_plot, y_pred_plot, alpha=0.6, s=20, c=y_true_plot, cmap='viridis')
        axes[i].plot([y_true_plot.min(), y_true_plot.max()],
                    [y_true_plot.min(), y_true_plot.max()], 'r--', lw=2)
        axes[i].set_xlabel('真实值 (MW)')
        axes[i].set_ylabel('预测值 (MW)')
        axes[i].set_title(f'{model_name}\n(R²={results_df.loc[model_name, "R²"]:.3f}, MAE={results_df.loc[model_name, "MAE"]:.3f})')
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'scatter_plot_matrix_enhanced.png'))
    plt.close()

    # 3. 误差分布小提琴图
    plt.figure(figsize=(15, 8))

    error_data = []
    model_names = []

    for model_name, y_pred in predictions.items():
        if is_daytime is not None:
            mask = is_daytime.astype(bool)
            errors = y_true[mask] - y_pred[mask]
        else:
            errors = y_true - y_pred
        error_data.append(errors)
        model_names.append(model_name)

    # 创建小提琴图
    parts = plt.violinplot(error_data, positions=range(len(model_names)), showmeans=True, showmedians=True)

    # 美化小提琴图
    for pc in parts['bodies']:
        pc.set_facecolor('lightblue')
        pc.set_alpha(0.7)

    plt.xticks(range(len(model_names)), model_names, rotation=45, ha='right')
    plt.title('各模型预测误差分布 (小提琴图)', fontsize=14)
    plt.xlabel('模型', fontsize=12)
    plt.ylabel('预测误差 (MW)', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.8)
    plt.tight_layout()

    plt.savefig(os.path.join(RESULTS_DIR, 'error_distribution_violin.png'))
    plt.close()

    # 4. 模型性能雷达图
    plt.figure(figsize=(10, 10))

    # 标准化指标用于雷达图
    metrics = ['MAE', 'RMSE', 'nRMSE', 'SMAPE', 'R²']
    normalized_results = results_df[metrics].copy()

    # 对于MAE, RMSE, nRMSE, SMAPE，值越小越好，所以取倒数并标准化
    for metric in ['MAE', 'RMSE', 'nRMSE', 'SMAPE']:
        max_val = normalized_results[metric].max()
        min_val = normalized_results[metric].min()
        normalized_results[metric] = 1 - (normalized_results[metric] - min_val) / (max_val - min_val)

    # R²已经在0-1之间，值越大越好，直接使用

    # 绘制雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合

    ax = plt.subplot(111, projection='polar')

    colors = plt.cm.Set3(np.linspace(0, 1, min(8, len(normalized_results))))

    for i, (model, row) in enumerate(normalized_results.head(8).iterrows()):  # 只显示前8个模型
        values = row.tolist()
        values += values[:1]  # 闭合

        ax.plot(angles, values, 'o-', linewidth=2, label=model, color=colors[i])
        ax.fill(angles, values, alpha=0.25, color=colors[i])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics)
    ax.set_ylim(0, 1)
    ax.set_title('模型性能雷达图 (标准化后)', size=16, y=1.1)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'model_performance_radar_enhanced.png'))
    plt.close()

def plot_time_series_with_timestamps(test_data, predictions, title_prefix):
    """绘制带真实时间戳的时间序列预测图"""

    # 获取前5个最佳模型
    best_models = list(predictions.keys())[:5]

    plt.figure(figsize=(20, 12))

    # 获取时间戳
    timestamps = test_data['date_time'].values
    y_true = test_data['power'].values

    # 绘制真实值
    plt.plot(timestamps, y_true, 'k-', label='真实值', linewidth=2, alpha=0.8)

    # 绘制不同模型的预测值
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    for i, model_name in enumerate(best_models):
        if model_name in predictions:
            y_pred = predictions[model_name]
            plt.plot(timestamps, y_pred, '--', label=f'{model_name}预测值',
                    linewidth=1.5, alpha=0.7, color=colors[i % len(colors)])

    plt.title(f'{title_prefix}光伏发电功率预测对比 (真实时间戳)', fontsize=16)
    plt.xlabel('时间', fontsize=14)
    plt.ylabel('功率 (MW)', fontsize=14)
    plt.legend(loc='best', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.xticks(rotation=45)

    # 添加白昼时段背景
    if 'is_daytime' in test_data.columns:
        is_daytime = test_data['is_daytime'].values
        daytime_periods = []
        start_idx = None

        for i, is_day in enumerate(is_daytime):
            if is_day and start_idx is None:
                start_idx = i
            elif not is_day and start_idx is not None:
                daytime_periods.append((start_idx, i-1))
                start_idx = None

        if start_idx is not None:
            daytime_periods.append((start_idx, len(is_daytime)-1))

        for start, end in daytime_periods:
            plt.axvspan(timestamps[start], timestamps[end], alpha=0.2, color='yellow', label='白昼时段' if start == daytime_periods[0][0] else "")

    plt.tight_layout()
    plt.savefig(os.path.join(RESULTS_DIR, 'time_series_with_timestamps.png'))
    plt.close()

def plot_monthly_predictions_all_models(test_month_data, predictions, results_df, station_id, test_indices):
    """分月绘制所有模型的预测结果"""
    month_names = {2: '2月', 5: '5月', 8: '8月', 11: '11月'}

    # 确保中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    for month, month_data in test_month_data.items():
        if len(month_data) == 0:
            continue

        print(f"绘制{month_names[month]}所有模型预测对比图...")

        # 获取该月的时间戳和真实值
        timestamps = month_data['date_time'].values
        y_true = month_data['power'].values
        is_daytime = month_data['is_daytime'].values

        # 创建图形
        plt.figure(figsize=(20, 12))

        # 绘制真实值
        plt.plot(timestamps, y_true, 'k-', label='真实值', linewidth=3, alpha=0.9)

        # 绘制所有模型的预测值
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink',
                 'gray', 'olive', 'cyan', 'magenta', 'yellow']

        model_count = 0
        for model_name, y_pred_full in predictions.items():
            if model_name == 'Ensemble':  # 跳过集成模型，避免图形过于复杂
                continue

            # 获取该月对应的预测值
            month_indices = month_data.index
            month_pred = []

            # 创建测试集索引到预测数组位置的映射
            test_indices_array = np.array(test_indices)

            # 找到在完整测试集中的位置
            for idx in month_indices:
                # 在全局测试集中找到对应位置
                global_pos = np.where(test_indices_array == idx)[0]
                if len(global_pos) > 0:
                    month_pred.append(y_pred_full[global_pos[0]])
                else:
                    # 如果找不到，尝试直接使用相对位置
                    try:
                        relative_pos = list(test_indices).index(idx)
                        month_pred.append(y_pred_full[relative_pos])
                    except (ValueError, IndexError):
                        month_pred.append(0)  # 最后才用0填充

            month_pred = np.array(month_pred)

            # 调试信息：检查预测值是否都是0
            if np.all(month_pred == 0):
                print(f"警告: {model_name}模型在{month}月的预测值全为0，可能存在索引映射问题")
                continue  # 跳过全0的预测

            # 获取该模型的R²值
            r2_value = results_df.loc[model_name, 'R²'] if model_name in results_df.index else 0

            # 绘制预测线
            color = colors[model_count % len(colors)]
            plt.plot(timestamps, month_pred, '--',
                    label=f'{model_name} (R²={r2_value:.3f})',
                    linewidth=2, alpha=0.8, color=color)
            model_count += 1

        # 添加白昼时段背景
        daytime_periods = []
        start_idx = None

        for i, is_day in enumerate(is_daytime):
            if is_day and start_idx is None:
                start_idx = i
            elif not is_day and start_idx is not None:
                daytime_periods.append((start_idx, i-1))
                start_idx = None

        if start_idx is not None:
            daytime_periods.append((start_idx, len(is_daytime)-1))

        for start, end in daytime_periods:
            plt.axvspan(timestamps[start], timestamps[end], alpha=0.15, color='yellow')

        # 添加白昼标记（只在第一个区域添加标签）
        if daytime_periods:
            start, end = daytime_periods[0]
            plt.axvspan(timestamps[start], timestamps[end], alpha=0.15, color='yellow', label='白昼时段')

        plt.title(f'站点{station_id} {month_names[month]}光伏发电功率预测对比 (所有模型)', fontsize=16, fontweight='bold')
        plt.xlabel('时间', fontsize=14)
        plt.ylabel('功率 (MW)', fontsize=14)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(rotation=45)

        # 调整布局
        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, f'month_{month}_all_models_prediction.png'),
                   bbox_inches='tight', dpi=300)
        plt.close()

def plot_monthly_predictions_best_model(test_month_data, predictions, results_df, station_id, test_indices):
    """分月绘制最佳模型的预测结果"""
    month_names = {2: '2月', 5: '5月', 8: '8月', 11: '11月'}

    # 确保中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 找到最佳模型（R²最高的模型）
    best_model = results_df['R²'].idxmax()
    best_r2 = results_df.loc[best_model, 'R²']
    best_mae = results_df.loc[best_model, 'MAE']

    print(f"最佳模型: {best_model} (R²={best_r2:.4f}, MAE={best_mae:.4f})")

    for month, month_data in test_month_data.items():
        if len(month_data) == 0:
            continue

        print(f"绘制{month_names[month]}最佳模型({best_model})预测图...")

        # 获取该月的时间戳和真实值
        timestamps = month_data['date_time'].values
        y_true = month_data['power'].values
        is_daytime = month_data['is_daytime'].values

        # 获取最佳模型的预测值
        month_indices = month_data.index
        month_pred = []

        # 创建测试集索引到预测数组位置的映射
        test_indices_array = np.array(test_indices)

        # 找到在完整测试集中的位置
        for idx in month_indices:
            global_pos = np.where(test_indices_array == idx)[0]
            if len(global_pos) > 0:
                month_pred.append(predictions[best_model][global_pos[0]])
            else:
                # 如果找不到，尝试直接使用相对位置
                try:
                    relative_pos = list(test_indices).index(idx)
                    month_pred.append(predictions[best_model][relative_pos])
                except (ValueError, IndexError):
                    month_pred.append(0)  # 最后才用0填充

        month_pred = np.array(month_pred)

        # 调试信息：检查预测值是否都是0
        if np.all(month_pred == 0):
            print(f"警告: {best_model}模型在{month}月的预测值全为0，可能存在索引映射问题")
            continue

        # 计算该月的R²
        if is_daytime is not None:
            mask = is_daytime.astype(bool)
            month_r2 = r2_score(y_true[mask], month_pred[mask]) if mask.sum() > 0 else 0
            month_mae = np.mean(np.abs(y_true[mask] - month_pred[mask])) if mask.sum() > 0 else 0
        else:
            month_r2 = r2_score(y_true, month_pred)
            month_mae = np.mean(np.abs(y_true - month_pred))

        # 创建图形
        plt.figure(figsize=(16, 10))

        # 绘制真实值
        plt.plot(timestamps, y_true, 'k-', label='真实值', linewidth=3, alpha=0.9)

        # 绘制最佳模型预测值
        plt.plot(timestamps, month_pred, 'r--',
                label=f'{best_model}预测值 (R²={month_r2:.3f}, MAE={month_mae:.3f}MW)',
                linewidth=3, alpha=0.8)

        # 添加白昼时段背景
        daytime_periods = []
        start_idx = None

        for i, is_day in enumerate(is_daytime):
            if is_day and start_idx is None:
                start_idx = i
            elif not is_day and start_idx is not None:
                daytime_periods.append((start_idx, i-1))
                start_idx = None

        if start_idx is not None:
            daytime_periods.append((start_idx, len(is_daytime)-1))

        for start, end in daytime_periods:
            plt.axvspan(timestamps[start], timestamps[end], alpha=0.2, color='yellow')

        # 添加白昼标记
        if daytime_periods:
            start, end = daytime_periods[0]
            plt.axvspan(timestamps[start], timestamps[end], alpha=0.2, color='yellow', label='白昼时段')

        # 添加误差填充区域
        plt.fill_between(timestamps, y_true - np.abs(y_true - month_pred),
                        y_true + np.abs(y_true - month_pred),
                        alpha=0.2, color='red', label='预测误差范围')

        plt.title(f'站点{station_id} {month_names[month]}光伏发电功率预测 - {best_model}模型',
                 fontsize=16, fontweight='bold')
        plt.xlabel('时间', fontsize=14)
        plt.ylabel('功率 (MW)', fontsize=14)
        plt.legend(fontsize=12, loc='best')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.xticks(rotation=45)

        # 添加统计信息文本框
        textstr = f'月度统计 (白昼时段):\nR² = {month_r2:.4f}\nMAE = {month_mae:.4f} MW\n数据点数 = {mask.sum() if is_daytime is not None else len(y_true)}'
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.8)
        plt.text(0.02, 0.98, textstr, transform=plt.gca().transAxes, fontsize=10,
                verticalalignment='top', bbox=props)

        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, f'month_{month}_best_model_{best_model}_prediction.png'),
                   dpi=300)
        plt.close()

    return best_model

def fast_ensemble(predictions_dict):
    """
    快速集成多个模型的预测结果
    简单加权平均
    """
    print("执行快速模型集成...")

    # 设置各模型权重
    weights = {
        'Persistence': 0.2,
        'LightGBM': 0.5,
        'XGBoost': 0.3
    }

    # 计算加权平均
    ensemble_pred = np.zeros_like(list(predictions_dict.values())[0])
    for model, pred in predictions_dict.items():
        if model in weights:
            ensemble_pred += weights[model] * pred

    return ensemble_pred

# 主函数
if __name__ == "__main__":
    print("正在执行光伏电站日前发电功率快速预测方案...")

    # 指定使用站点1的数据
    station_id = 1

    # 加载数据
    start_time = time.time()
    df = load_station_data(station_id)
    print(f"成功加载站点{station_id}数据，共{len(df)}条记录")

    # 特征工程
    df_features = prepare_features(df)
    print(f"特征工程完成，共{df_features.shape[1]}个特征")

    # 划分训练集和测试集
    train_indices, test_indices, test_month_data = get_train_test_split(df_features)
    X_train = df_features.loc[train_indices]
    X_test = df_features.loc[test_indices]
    print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")

    # 获取测试集日期和时间
    test_dates = sorted(X_test['date'].unique())
    print(f"测试日期: {test_dates}")
    print(f"测试月份数据: {list(test_month_data.keys())}")

    # 保存白昼标记，用于评估和可视化
    is_daytime = X_test['is_daytime'].values

    # 准备标签
    y_train = X_train['power'].values
    y_test = X_test['power'].values

    # 测试集时间点
    test_times = X_test['date_time']

    # 存储所有模型的预测结果
    predictions = {}
    model_times = {}

    # 1. 运行Persistence模型（基准线）
    print("\n=== 运行Persistence基准模型 ===")
    start_time = time.time()
    persistence_pred = persistence_model(X_train, test_dates)
    persistence_time = time.time() - start_time
    predictions['Persistence'] = persistence_pred
    model_times['Persistence'] = persistence_time
    print(f"Persistence模型完成，耗时: {persistence_time:.2f}秒")

    # 2. 运行快速LightGBM模型
    print("\n=== 运行快速LightGBM模型 ===")
    start_time = time.time()
    lgbm_pred, _ = fast_lightgbm_model(X_train, y_train, X_test)
    lgbm_time = time.time() - start_time
    predictions['LightGBM'] = lgbm_pred
    model_times['LightGBM'] = lgbm_time
    print(f"LightGBM模型完成，耗时: {lgbm_time:.2f}秒")

    # 3. 运行快速XGBoost模型
    print("\n=== 运行快速XGBoost模型 ===")
    start_time = time.time()
    xgb_pred, _ = fast_xgboost_model(X_train, y_train, X_test)
    xgb_time = time.time() - start_time
    predictions['XGBoost'] = xgb_pred
    model_times['XGBoost'] = xgb_time
    print(f"XGBoost模型完成，耗时: {xgb_time:.2f}秒")

    # 4. 运行快速CatBoost模型
    print("\n=== 运行快速CatBoost模型 ===")
    try:
        start_time = time.time()
        catboost_pred, _ = fast_catboost_model(X_train, y_train, X_test)
        catboost_time = time.time() - start_time
        predictions['CatBoost'] = catboost_pred
        model_times['CatBoost'] = catboost_time
        print(f"CatBoost模型完成，耗时: {catboost_time:.2f}秒")
    except Exception as e:
        print(f"CatBoost模型运行出错: {e}")

    # 5. 运行快速随机森林模型
    print("\n=== 运行快速随机森林模型 ===")
    start_time = time.time()
    rf_pred, _ = fast_random_forest_model(X_train, y_train, X_test)
    rf_time = time.time() - start_time
    predictions['RandomForest'] = rf_pred
    model_times['RandomForest'] = rf_time
    print(f"随机森林模型完成，耗时: {rf_time:.2f}秒")

    # 6. 运行快速线性模型
    print("\n=== 运行快速线性模型 ===")
    start_time = time.time()
    linear_preds = fast_linear_models(X_train, y_train, X_test)
    linear_time = time.time() - start_time
    for name, pred in linear_preds.items():
        predictions[name] = pred
        model_times[name] = linear_time / len(linear_preds)
    print(f"线性模型完成，耗时: {linear_time:.2f}秒")

    # 7. 运行快速集成模型
    print("\n=== 运行快速集成模型 ===")
    start_time = time.time()
    ensemble_preds = fast_ensemble_models(X_train, y_train, X_test)
    ensemble_time = time.time() - start_time
    for name, pred in ensemble_preds.items():
        predictions[name] = pred
        model_times[name] = ensemble_time / len(ensemble_preds)
    print(f"集成模型完成，耗时: {ensemble_time:.2f}秒")

    # 8. 运行快速神经网络模型
    print("\n=== 运行快速神经网络模型 ===")
    try:
        start_time = time.time()
        nn_pred = fast_neural_network_model(X_train, y_train, X_test)
        nn_time = time.time() - start_time
        predictions['FastNN'] = nn_pred
        model_times['FastNN'] = nn_time
        print(f"神经网络模型完成，耗时: {nn_time:.2f}秒")
    except Exception as e:
        print(f"神经网络模型运行出错: {e}")

    # 9. 快速集成所有模型
    print("\n=== 运行模型集成 ===")
    ensemble_pred = fast_ensemble(predictions)
    predictions['Ensemble'] = ensemble_pred

    # 只评估白昼时段的模型性能
    print("\n=== 模型性能评估 ===")
    daytime_results = evaluate_models_enhanced(y_test, predictions, is_daytime)
    print("\n白昼时段模型评估结果:")
    print(daytime_results)

    # 保存评估结果
    daytime_results.to_csv(os.path.join(RESULTS_DIR, 'daytime_model_evaluation.csv'))

    # 保存运行时间
    pd.DataFrame({'Runtime(s)': model_times}).to_csv(os.path.join(RESULTS_DIR, 'model_runtime.csv'))

    # 绘制原始预测对比图
    plot_prediction_comparison(
        y_test,
        predictions,
        is_daytime,
        f"站点{station_id}预测效果对比(标记白昼时段)",
        os.path.join(RESULTS_DIR, 'prediction_comparison_with_daytime.png')
    )

    # 绘制增强的可视化图表
    print("\n=== 生成增强可视化图表 ===")
    plot_enhanced_visualizations(y_test, predictions, is_daytime, daytime_results)

    # 绘制带时间戳的时间序列图
    plot_time_series_with_timestamps(X_test, predictions, f"站点{station_id}")

    # 分月绘制所有模型的预测结果
    print("\n=== 分月绘制所有模型预测对比图 ===")
    plot_monthly_predictions_all_models(test_month_data, predictions, daytime_results, station_id, test_indices)

    # 分月绘制最佳模型的预测结果
    print("\n=== 分月绘制最佳模型预测图 ===")
    best_model_name = plot_monthly_predictions_best_model(test_month_data, predictions, daytime_results, station_id, test_indices)

    # 将预测结果保存为表2格式
    result_table = save_predictions_to_table(
        test_dates,
        X_test['date_time'].values,  # 使用真实时间戳
        y_test,
        predictions,
        is_daytime
    )

    total_time = time.time() - start_time
    print(f"\n快速预测方案完成！总耗时: {total_time:.2f}秒")
    print(f"结果已保存到 {RESULTS_DIR} 目录")

    # 保存增强的总结果
    summary = pd.DataFrame({
        'Model': daytime_results.index,
        'MAE': daytime_results['MAE'],
        'RMSE': daytime_results['RMSE'],
        'nRMSE': daytime_results['nRMSE'],
        'SMAPE': daytime_results['SMAPE'],
        'R²': daytime_results['R²'],
        'Runtime(s)': [model_times.get(model, 0) for model in daytime_results.index]
    })

    # 计算相对于Persistence基准的改进百分比
    if 'Persistence' in daytime_results.index:
        base_mae = daytime_results.loc['Persistence', 'MAE']
        base_r2 = daytime_results.loc['Persistence', 'R²']
        summary['MAE_Improvement(%)'] = (base_mae - summary['MAE']) / base_mae * 100
        summary['R²_Improvement'] = summary['R²'] - base_r2

    # 添加综合排名
    ranking_score = (
        summary['MAE'].rank() +
        summary['RMSE'].rank() +
        summary['nRMSE'].rank() +
        summary['SMAPE'].rank() +
        summary['R²'].rank(ascending=False)
    ) / 5
    summary['综合排名'] = ranking_score.rank()

    # 按综合排名排序
    summary = summary.sort_values('综合排名')

    print("\n=== 模型性能总结 (白昼时段) ===")
    print(summary.round(4))
    summary.to_csv(os.path.join(RESULTS_DIR, 'enhanced_model_summary.csv'), index=False)

    # 显示最佳模型
    best_model = summary.iloc[0]['Model']
    best_mae = summary.iloc[0]['MAE']
    best_r2 = summary.iloc[0]['R²']

    print(f"\n🏆 最佳模型: {best_model}")
    print(f"   MAE: {best_mae:.4f} MW")
    print(f"   R²: {best_r2:.4f}")

    if 'Persistence' in daytime_results.index:
        improvement = summary.iloc[0]['MAE_Improvement(%)']
        print(f"   相比基准模型MAE改进: {improvement:.1f}%")

    print(f"\n📊 共测试了 {len(predictions)} 个模型")
    print(f"📈 生成了 {len([f for f in os.listdir(RESULTS_DIR) if f.endswith('.png')])} 个可视化图表")
    print(f"📋 保存了 {len([f for f in os.listdir(RESULTS_DIR) if f.endswith('.csv')])} 个数据表格")