#!/usr/bin/env python3
"""
运行4090优化版本的光伏发电功率预测脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入主模块
    from q2_4090 import *
    
    print("=" * 60)
    print("光伏电站发电功率日前预测 - 4090优化版")
    print("=" * 60)
    print()
    
    # 检查CUDA环境
    print("环境检查:")
    print(f"- Python版本: {sys.version}")
    print(f"- PyTorch版本: {torch.__version__}")
    print(f"- CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"- GPU设备: {torch.cuda.get_device_name()}")
        print(f"- GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        print(f"- CUDA版本: {torch.version.cuda}")
    else:
        print("- 将使用CPU进行计算")
    
    print()
    print("开始执行预测任务...")
    print()
    
    # 执行主程序
    # 这里会自动调用 if __name__ == "__main__": 部分的代码
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保安装了所有必需的依赖包:")
    print("pip install pandas numpy matplotlib seaborn scikit-learn")
    print("pip install lightgbm xgboost torch torchvision")
    print("pip install statsmodels pmdarima prophet tqdm shap")
    
except Exception as e:
    print(f"运行错误: {e}")
    import traceback
    traceback.print_exc()
