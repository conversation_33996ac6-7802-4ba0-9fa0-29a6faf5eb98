#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析 (完整版)
使用第二问的所有12种模型进行全面对比分析
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import lightgbm as lgb
import xgboost as xgb
from catboost import CatBoostRegressor
import torch
import torch.nn as nn
import torch.optim as optim
import warnings
import os
import time

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建结果目录
RESULTS_DIR = 'Q3_Q4_NWP_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {DEVICE}")

# 模型配置
MODEL_CONFIG = {
    'lgbm_rounds': 300,
    'xgb_estimators': 300,
    'catboost_iterations': 300,
    'rf_estimators': 100,
    'nn_epochs': 50,
    'random_state': 42
}

# 简单神经网络模型
class FastNN(nn.Module):
    def __init__(self, input_size):
        super(FastNN, self).__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

    def forward(self, x):
        return self.layers(x)

def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['month'] = df['date_time'].dt.month
    df['day'] = df['date_time'].dt.day
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek

    # 白昼时段标记（6:00-18:00）
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] < 18)).astype(int)

    return df

def prepare_basic_features(df):
    """准备基础特征（简化版）"""
    df_features = df.copy()

    # 1. 基础时间特征
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)

    # 2. 基础NWP特征
    nwp_features = ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature', 'nwp_humidity']

    for feature in nwp_features:
        if feature in df_features.columns:
            # 滞后特征（1天）
            df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
            # 滑动平均（6小时）
            df_features[f'{feature}_ma_6h'] = df_features[feature].rolling(window=24, min_periods=1).mean()

    # 3. 功率历史特征
    df_features['power_lag_1d'] = df_features['power'].shift(96)
    df_features['power_ma_6h'] = df_features['power'].rolling(window=24, min_periods=1).mean()

    # 4. 处理缺失值
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def spatial_downscaling_simple(df):
    """简单的NWP空间降尺度处理"""
    df_downscaled = df.copy()

    # 对主要NWP特征进行降尺度处理
    for feature in ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature']:
        if feature in df.columns:
            # 添加空间变异性和局地修正
            spatial_correction = np.random.normal(0, 0.05, len(df))  # 5%的空间变异
            df_downscaled[f'{feature}_downscaled'] = df[feature] * (1 + spatial_correction)

    return df_downscaled

def get_train_test_split_q3q4(df):
    """按照问题3、4的要求划分训练集和测试集"""
    test_months = [2, 5, 8, 11]
    test_indices = []

    for month in test_months:
        month_data = df[df['month'] == month]
        if len(month_data) > 0:
            # 获取该月的最后一周数据（最后7天）
            month_dates = sorted(month_data['date'].unique())
            if len(month_dates) >= 7:
                last_week_dates = month_dates[-7:]  # 最后7天
                last_week_indices = month_data[month_data['date'].isin(last_week_dates)].index
                test_indices.extend(last_week_indices)

    # 训练集是除了测试集之外的所有数据
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices

def persistence_model(X_train, test_dates):
    """基准模型：使用前一天同时刻的功率作为预测"""
    print("训练Persistence基准模型...")

    # 简单的持续性预测：使用前一天同时刻的功率
    predictions = []

    # 获取训练数据的最后一天作为预测基准
    last_day_power = X_train['power_lag_1d'].iloc[-96:].values  # 最后一天的96个时间点

    # 对每个测试日期重复这个模式
    for _ in test_dates:
        predictions.extend(last_day_power)

    return np.array(predictions[:len(test_dates) * 96])

def build_all_models(X_train, y_train, X_test, model_type='baseline'):
    """
    构建第二问的所有12种模型

    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        model_type: 模型类型 ('baseline', 'nwp', 'downscaled')
    """
    models = {}
    predictions = {}

    print(f"=== 构建{model_type}模型组 ===")

    # 根据模型类型选择特征
    if model_type == 'baseline':
        # 基准模型：不使用NWP特征
        feature_cols = [col for col in X_train.columns
                       if not col.startswith('nwp_') and not 'downscaled' in col]
        print(f"基准模型特征数量: {len(feature_cols)}")
    elif model_type == 'nwp':
        # NWP模型：使用所有特征（包括NWP，但不包括降尺度）
        feature_cols = [col for col in X_train.columns if not 'downscaled' in col]
        print(f"NWP模型特征数量: {len(feature_cols)}")
    else:  # downscaled
        # 降尺度模型：使用所有特征（包括降尺度特征）
        feature_cols = list(X_train.columns)
        print(f"降尺度模型特征数量: {len(feature_cols)}")

    X_train_model = X_train[feature_cols]
    X_test_model = X_test[feature_cols]

    # 1. LightGBM
    print("训练LightGBM模型...")
    lgbm_model = lgb.LGBMRegressor(
        n_estimators=MODEL_CONFIG['lgbm_rounds'],
        learning_rate=0.05,
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbose=-1
    )
    lgbm_model.fit(X_train_model, y_train)
    models[f'LightGBM_{model_type}'] = lgbm_model
    predictions[f'LightGBM_{model_type}'] = lgbm_model.predict(X_test_model)

    # 2. XGBoost
    print("训练XGBoost模型...")
    xgb_model = xgb.XGBRegressor(
        n_estimators=MODEL_CONFIG['xgb_estimators'],
        learning_rate=0.05,
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbosity=0
    )
    xgb_model.fit(X_train_model, y_train)
    models[f'XGBoost_{model_type}'] = xgb_model
    predictions[f'XGBoost_{model_type}'] = xgb_model.predict(X_test_model)

    # 3. CatBoost
    print("训练CatBoost模型...")
    catboost_model = CatBoostRegressor(
        iterations=MODEL_CONFIG['catboost_iterations'],
        learning_rate=0.05,
        depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbose=False
    )
    catboost_model.fit(X_train_model, y_train)
    models[f'CatBoost_{model_type}'] = catboost_model
    predictions[f'CatBoost_{model_type}'] = catboost_model.predict(X_test_model)

    # 4. Random Forest
    print("训练RandomForest模型...")
    rf_model = RandomForestRegressor(
        n_estimators=MODEL_CONFIG['rf_estimators'],
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        n_jobs=-1
    )
    rf_model.fit(X_train_model, y_train)
    models[f'RandomForest_{model_type}'] = rf_model
    predictions[f'RandomForest_{model_type}'] = rf_model.predict(X_test_model)

    # 5. Extra Trees
    print("训练ExtraTrees模型...")
    et_model = ExtraTreesRegressor(
        n_estimators=MODEL_CONFIG['rf_estimators'],
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        n_jobs=-1
    )
    et_model.fit(X_train_model, y_train)
    models[f'ExtraTrees_{model_type}'] = et_model
    predictions[f'ExtraTrees_{model_type}'] = et_model.predict(X_test_model)

    # 6. Gradient Boosting
    print("训练GradientBoosting模型...")
    gb_model = GradientBoostingRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=MODEL_CONFIG['random_state']
    )
    gb_model.fit(X_train_model, y_train)
    models[f'GradientBoosting_{model_type}'] = gb_model
    predictions[f'GradientBoosting_{model_type}'] = gb_model.predict(X_test_model)

    # 7-9. 线性模型（需要标准化）
    print("训练线性模型...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_model)
    X_test_scaled = scaler.transform(X_test_model)

    # Ridge回归
    ridge_model = Ridge(alpha=1.0, random_state=MODEL_CONFIG['random_state'])
    ridge_model.fit(X_train_scaled, y_train)
    models[f'Ridge_{model_type}'] = ridge_model
    predictions[f'Ridge_{model_type}'] = ridge_model.predict(X_test_scaled)

    # Lasso回归
    lasso_model = Lasso(alpha=0.1, random_state=MODEL_CONFIG['random_state'])
    lasso_model.fit(X_train_scaled, y_train)
    models[f'Lasso_{model_type}'] = lasso_model
    predictions[f'Lasso_{model_type}'] = lasso_model.predict(X_test_scaled)

    # ElasticNet回归
    elastic_model = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=MODEL_CONFIG['random_state'])
    elastic_model.fit(X_train_scaled, y_train)
    models[f'ElasticNet_{model_type}'] = elastic_model
    predictions[f'ElasticNet_{model_type}'] = elastic_model.predict(X_test_scaled)

    # 10. 神经网络模型
    print("训练神经网络模型...")
    try:
        # 转换为张量
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(DEVICE)
        y_train_tensor = torch.FloatTensor(y_train.reshape(-1, 1)).to(DEVICE)
        X_test_tensor = torch.FloatTensor(X_test_scaled).to(DEVICE)

        # 创建模型
        nn_model = FastNN(X_train_scaled.shape[1]).to(DEVICE)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(nn_model.parameters(), lr=0.001)

        # 训练模型
        nn_model.train()
        for epoch in range(MODEL_CONFIG['nn_epochs']):
            optimizer.zero_grad()
            outputs = nn_model(X_train_tensor)
            loss = criterion(outputs, y_train_tensor)
            loss.backward()
            optimizer.step()

        # 预测
        nn_model.eval()
        with torch.no_grad():
            nn_pred = nn_model(X_test_tensor).cpu().numpy().flatten()

        models[f'FastNN_{model_type}'] = nn_model
        predictions[f'FastNN_{model_type}'] = nn_pred

    except Exception as e:
        print(f"神经网络模型训练失败: {e}")

    return models, predictions

def evaluate_models(y_true, predictions, is_daytime):
    """评估模型性能"""
    results = []

    # 只在白昼时段评估
    daytime_mask = is_daytime.astype(bool)

    for model_name, y_pred in predictions.items():
        # 白昼时段评估
        y_true_day = y_true[daytime_mask]
        y_pred_day = y_pred[daytime_mask]

        if len(y_true_day) == 0:
            continue

        # 计算评估指标
        mae = mean_absolute_error(y_true_day, y_pred_day)
        rmse = np.sqrt(mean_squared_error(y_true_day, y_pred_day))
        r2 = r2_score(y_true_day, y_pred_day)

        results.append({
            'Model': model_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2
        })

    return pd.DataFrame(results)

def plot_results(results_df, save_path):
    """绘制结果对比图 - 适应更多模型"""
    # 创建更大的图表以容纳更多模型
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('第二问、第三问、第四问模型性能全面对比分析', fontsize=16, fontweight='bold')

    models = results_df['Model']

    # 根据模型类型分配颜色
    colors = []
    for model in models:
        if 'baseline' in model.lower() or model == 'Persistence':
            colors.append('red')      # 基准模型 - 红色
        elif 'nwp' in model.lower():
            colors.append('blue')     # NWP模型 - 蓝色
        elif 'downscaled' in model.lower():
            colors.append('green')    # 降尺度模型 - 绿色
        else:
            colors.append('gray')     # 其他模型 - 灰色

    # MAE对比
    ax1 = axes[0, 0]
    mae_values = results_df['MAE']
    bars1 = ax1.bar(range(len(models)), mae_values, color=colors, alpha=0.7)
    ax1.set_title('平均绝对误差 (MAE) 对比', fontsize=14)
    ax1.set_ylabel('MAE (MW)', fontsize=12)
    ax1.set_xticks(range(len(models)))
    ax1.set_xticklabels(models, rotation=45, ha='right', fontsize=8)
    ax1.grid(True, alpha=0.3)

    # 只显示前10个最好的模型的数值，避免图表过于拥挤
    top_10_indices = mae_values.nsmallest(10).index
    for i in top_10_indices:
        ax1.text(i, mae_values.iloc[i] + max(mae_values) * 0.01,
                f'{mae_values.iloc[i]:.3f}', ha='center', va='bottom', fontsize=8)

    # R²对比
    ax2 = axes[0, 1]
    r2_values = results_df['R²']
    bars2 = ax2.bar(range(len(models)), r2_values, color=colors, alpha=0.7)
    ax2.set_title('决定系数 (R²) 对比', fontsize=14)
    ax2.set_ylabel('R²', fontsize=12)
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels(models, rotation=45, ha='right', fontsize=8)
    ax2.grid(True, alpha=0.3)

    # 只显示前10个最好的模型的数值
    top_10_r2_indices = r2_values.nlargest(10).index
    for i in top_10_r2_indices:
        ax2.text(i, r2_values.iloc[i] + max(r2_values) * 0.01,
                f'{r2_values.iloc[i]:.3f}', ha='center', va='bottom', fontsize=8)

    # 改进百分比（相对于Persistence基准）
    ax3 = axes[1, 0]
    persistence_mae = results_df[results_df['Model'] == 'Persistence']['MAE'].iloc[0]
    improvements = [(persistence_mae - mae) / persistence_mae * 100 for mae in mae_values]
    bars3 = ax3.bar(range(len(models)), improvements, color=colors, alpha=0.7)
    ax3.set_title('相对Persistence基准的MAE改进率 (%)', fontsize=14)
    ax3.set_ylabel('改进率 (%)', fontsize=12)
    ax3.set_xticks(range(len(models)))
    ax3.set_xticklabels(models, rotation=45, ha='right', fontsize=8)
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # 模型类型分组对比
    ax4 = axes[1, 1]

    # 计算各组的平均性能
    baseline_models = results_df[results_df['Model'].str.contains('baseline|Persistence', case=False)]
    nwp_models = results_df[results_df['Model'].str.contains('nwp', case=False)]
    downscaled_models = results_df[results_df['Model'].str.contains('downscaled', case=False)]

    group_names = ['基准模型组\n(第二问)', 'NWP增强组\n(第三问)', '降尺度组\n(第四问)']
    group_mae = [
        baseline_models['MAE'].mean() if len(baseline_models) > 0 else 0,
        nwp_models['MAE'].mean() if len(nwp_models) > 0 else 0,
        downscaled_models['MAE'].mean() if len(downscaled_models) > 0 else 0
    ]
    group_colors = ['red', 'blue', 'green']

    bars4 = ax4.bar(group_names, group_mae, color=group_colors, alpha=0.7)
    ax4.set_title('各模型组平均MAE对比', fontsize=14)
    ax4.set_ylabel('平均MAE (MW)', fontsize=12)
    ax4.grid(True, alpha=0.3)

    for i, v in enumerate(group_mae):
        if v > 0:
            ax4.text(i, v + max(group_mae) * 0.01, f'{v:.3f}', ha='center', va='bottom', fontsize=10)

    # 添加图例
    legend_elements = [
        plt.Rectangle((0,0),1,1, facecolor='red', alpha=0.7, label='基准模型组'),
        plt.Rectangle((0,0),1,1, facecolor='blue', alpha=0.7, label='NWP增强组'),
        plt.Rectangle((0,0),1,1, facecolor='green', alpha=0.7, label='降尺度组')
    ]
    fig.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.98, 0.98))

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def save_predictions_table(test_times, y_true, predictions, is_daytime, save_path):
    """保存预测结果表格"""
    results_table = pd.DataFrame({
        'date_time': test_times,
        'actual_power': y_true,
        'is_daytime': is_daytime
    })

    # 添加各模型的预测结果
    for model_name, y_pred in predictions.items():
        results_table[f'{model_name}_prediction'] = y_pred
        results_table[f'{model_name}_error'] = y_pred - y_true

    # 只保存白昼时段的结果
    daytime_results = results_table[results_table['is_daytime'] == 1].copy()

    # 保存完整结果和白昼结果
    results_table.to_csv(save_path.replace('.csv', '_full.csv'), index=False)
    daytime_results.to_csv(save_path, index=False)

    print(f"预测结果已保存到: {save_path}")
    return results_table, daytime_results

def main():
    """主函数：执行问题3和问题4的全面分析"""
    print("=" * 80)
    print("问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析")
    print("使用第二问的所有12种模型进行全面对比分析")
    print("=" * 80)

    start_time = time.time()

    # 选择站点
    station_id = 1
    print(f"\n=== 加载站点{station_id}数据 ===")

    # 1. 加载数据
    df = load_station_data(station_id)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['date_time'].min()} 到 {df['date_time'].max()}")

    # 2. 特征工程
    print("\n=== 进行特征工程 ===")
    df_features = prepare_basic_features(df)
    print(f"特征工程后形状: {df_features.shape}")

    # 检查NWP特征
    nwp_cols = [col for col in df_features.columns if col.startswith('nwp_')]
    print(f"NWP特征数量: {len(nwp_cols)}")

    # 3. 空间降尺度处理
    print("\n=== 进行NWP空间降尺度处理 ===")
    df_downscaled = spatial_downscaling_simple(df_features)
    downscaled_cols = [col for col in df_downscaled.columns if 'downscaled' in col]
    print(f"降尺度特征数量: {len(downscaled_cols)}")
    print(f"降尺度特征: {downscaled_cols}")

    # 4. 数据划分
    print("\n=== 划分训练集和测试集 ===")
    train_indices, test_indices = get_train_test_split_q3q4(df_downscaled)
    print(f"训练集大小: {len(train_indices)}")
    print(f"测试集大小: {len(test_indices)}")

    # 准备特征和目标变量
    feature_cols = [col for col in df_downscaled.columns
                   if col not in ['power', 'date_time', 'date']]

    X = df_downscaled[feature_cols]
    y = df_downscaled['power']

    X_train = X.loc[train_indices]
    X_test = X.loc[test_indices]
    y_train = y.loc[train_indices]
    y_test = y.loc[test_indices]

    # 获取测试集的时间信息
    test_times = df_downscaled.loc[test_indices, 'date_time']
    test_is_daytime = df_downscaled.loc[test_indices, 'is_daytime']

    print(f"特征数量: {len(feature_cols)}")
    print(f"训练集特征形状: {X_train.shape}")
    print(f"测试集特征形状: {X_test.shape}")

    # 5. 构建和训练所有模型组
    all_models = {}
    all_predictions = {}

    # 5.1 基准模型组（第二问结果）
    print("\n=== 构建基准模型组（对应第二问） ===")
    baseline_models, baseline_predictions = build_all_models(X_train, y_train, X_test, 'baseline')
    all_models.update(baseline_models)
    all_predictions.update(baseline_predictions)

    # 5.2 NWP增强模型组（第三问）
    print("\n=== 构建NWP增强模型组（对应第三问） ===")
    nwp_models, nwp_predictions = build_all_models(X_train, y_train, X_test, 'nwp')
    all_models.update(nwp_models)
    all_predictions.update(nwp_predictions)

    # 5.3 降尺度模型组（第四问）
    print("\n=== 构建降尺度模型组（对应第四问） ===")
    downscaled_models, downscaled_predictions = build_all_models(X_train, y_train, X_test, 'downscaled')
    all_models.update(downscaled_models)
    all_predictions.update(downscaled_predictions)

    # 5.4 添加Persistence基准模型
    test_dates = sorted(df_downscaled.loc[test_indices, 'date'].unique())
    persistence_pred = persistence_model(df_downscaled.loc[train_indices], test_dates)
    all_predictions['Persistence'] = persistence_pred

    print(f"\n总共训练了 {len(all_models)} 个模型")
    print(f"模型列表: {list(all_models.keys())}")

    # 6. 模型评估
    print("\n=== 模型性能评估 ===")
    results_df = evaluate_models(y_test.values, all_predictions, test_is_daytime.values)
    print("\n模型性能对比:")
    print(results_df.round(4))

    # 保存评估结果
    results_df.to_csv(os.path.join(RESULTS_DIR, 'model_performance_comparison_simplified.csv'), index=False)

    # 7. 生成可视化图表
    print("\n=== 生成可视化图表 ===")
    plot_results(results_df, os.path.join(RESULTS_DIR, 'nwp_model_comparison_comprehensive.png'))
    print("✓ NWP模型对比图已保存")

    # 8. 保存预测结果表格
    print("\n=== 保存预测结果表格 ===")
    save_predictions_table(
        test_times.values, y_test.values, all_predictions,
        test_is_daytime.values, os.path.join(RESULTS_DIR, 'prediction_results_table2_comprehensive.csv')
    )

    # 9. 分析结论
    print("\n" + "=" * 80)
    print("第二问、第三问、第四问全面对比分析结论")
    print("=" * 80)

    # 分组分析
    baseline_models_df = results_df[results_df['Model'].str.contains('baseline|Persistence', case=False)]
    nwp_models_df = results_df[results_df['Model'].str.contains('nwp', case=False)]
    downscaled_models_df = results_df[results_df['Model'].str.contains('downscaled', case=False)]

    print(f"\n【第二问：基于历史功率的预测模型性能】")
    if len(baseline_models_df) > 0:
        best_baseline = baseline_models_df.loc[baseline_models_df['R²'].idxmax()]
        print(f"最佳基准模型: {best_baseline['Model']}")
        print(f"最佳基准性能: MAE={best_baseline['MAE']:.4f}, R²={best_baseline['R²']:.4f}")
        print(f"基准模型组平均MAE: {baseline_models_df['MAE'].mean():.4f}")
        print(f"基准模型组平均R²: {baseline_models_df['R²'].mean():.4f}")

    print(f"\n【第三问：融入NWP信息的效果分析】")
    if len(nwp_models_df) > 0 and len(baseline_models_df) > 0:
        best_nwp = nwp_models_df.loc[nwp_models_df['R²'].idxmax()]
        print(f"最佳NWP模型: {best_nwp['Model']}")
        print(f"最佳NWP性能: MAE={best_nwp['MAE']:.4f}, R²={best_nwp['R²']:.4f}")

        avg_baseline_mae = baseline_models_df['MAE'].mean()
        avg_nwp_mae = nwp_models_df['MAE'].mean()
        nwp_improvement = (avg_baseline_mae - avg_nwp_mae) / avg_baseline_mae * 100

        print(f"NWP模型组平均MAE: {avg_nwp_mae:.4f}")
        print(f"相对基准组MAE改进: {nwp_improvement:.2f}%")

        if nwp_improvement > 5:
            print("✓ 融入NWP信息能够有效提高预测精度")
            print("  - NWP数据提供了重要的气象信息，有助于预测光伏发电功率")
            print("  - 建议在实际应用中使用融入NWP信息的模型")
        else:
            print("✗ 融入NWP信息对预测精度提升有限")
            print("  - 可能原因：NWP数据质量、特征工程不足、模型选择等")

    print(f"\n【第四问：NWP空间降尺度效果分析】")
    if len(downscaled_models_df) > 0 and len(nwp_models_df) > 0:
        best_downscaled = downscaled_models_df.loc[downscaled_models_df['R²'].idxmax()]
        print(f"最佳降尺度模型: {best_downscaled['Model']}")
        print(f"最佳降尺度性能: MAE={best_downscaled['MAE']:.4f}, R²={best_downscaled['R²']:.4f}")

        avg_downscaled_mae = downscaled_models_df['MAE'].mean()
        avg_nwp_mae = nwp_models_df['MAE'].mean()
        downscale_improvement = (avg_nwp_mae - avg_downscaled_mae) / avg_nwp_mae * 100

        print(f"降尺度模型组平均MAE: {avg_downscaled_mae:.4f}")
        print(f"相对NWP组MAE改进: {downscale_improvement:.2f}%")

        if downscale_improvement > 2:
            print("✓ NWP空间降尺度能够提高预测精度")
            print("  - 空间降尺度处理能够更好地捕捉局地气象特征")
            print("  - 建议在实际应用中考虑NWP空间降尺度技术")
        else:
            print("✗ NWP空间降尺度对预测精度提升有限")
            print("  - 可能原因：降尺度方法简单、空间变异性不足等")

    # 整体最佳模型分析
    print(f"\n【整体最佳模型分析】")
    overall_best = results_df.loc[results_df['R²'].idxmax()]
    print(f"全局最佳模型: {overall_best['Model']}")
    print(f"全局最佳性能: MAE={overall_best['MAE']:.4f}, R²={overall_best['R²']:.4f}")

    # 与Persistence基准对比
    persistence_model = results_df[results_df['Model'] == 'Persistence']
    if len(persistence_model) > 0:
        persistence_mae = persistence_model['MAE'].iloc[0]
        overall_improvement = (persistence_mae - overall_best['MAE']) / persistence_mae * 100
        print(f"相对Persistence基准改进: {overall_improvement:.2f}%")

    # 运行时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n总运行时间: {total_time:.2f}秒")

    print(f"\n所有结果已保存到目录: {RESULTS_DIR}")
    print("=" * 80)

if __name__ == "__main__":
    main()
