#!/usr/bin/env python3
"""
测试4090优化版本的光伏发电功率预测代码
"""

import os
import sys
import pandas as pd
import numpy as np
import torch

def test_environment():
    """测试运行环境"""
    print("=== 环境测试 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查必要的包
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'sklearn', 'lightgbm', 'xgboost', 'torch'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {missing_packages}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    # 检查CUDA
    print(f"\nCUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU设备: {torch.cuda.get_device_name()}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    
    return True

def test_data_loading():
    """测试数据加载"""
    print("\n=== 数据加载测试 ===")
    
    # 检查数据文件
    data_file = 'dataset/station01.csv'
    if not os.path.exists(data_file):
        print(f"✗ 数据文件不存在: {data_file}")
        return False
    
    try:
        # 尝试加载数据
        df = pd.read_csv(data_file)
        print(f"✓ 成功加载数据: {df.shape}")
        
        # 检查必要的列
        required_columns = ['date_time', 'power']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            print(f"✗ 缺失必要列: {missing_columns}")
            return False
        
        print(f"✓ 数据列检查通过")
        print(f"✓ 数据时间范围: {df['date_time'].min()} 到 {df['date_time'].max()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False

def test_basic_functions():
    """测试基本功能"""
    print("\n=== 基本功能测试 ===")
    
    try:
        # 导入主要函数
        from q2_4090 import load_station_data, prepare_features, get_train_test_split
        
        print("✓ 函数导入成功")
        
        # 测试数据加载
        df = load_station_data(1)
        print(f"✓ 数据加载: {df.shape}")
        
        # 测试特征工程
        df_features = prepare_features(df)
        print(f"✓ 特征工程: {df_features.shape}")
        
        # 测试数据划分
        train_indices, test_indices, test_month_data = get_train_test_split(df_features)
        print(f"✓ 数据划分: 训练集{len(train_indices)}, 测试集{len(test_indices)}")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_creation():
    """测试模型创建"""
    print("\n=== 模型创建测试 ===")
    
    try:
        from q2_4090 import AdvancedTCN
        
        # 创建测试模型
        model = AdvancedTCN(input_size=10, output_size=1)
        print("✓ TCN模型创建成功")
        
        # 测试前向传播
        test_input = torch.randn(2, 24, 10)  # batch_size=2, seq_len=24, features=10
        output = model(test_input)
        print(f"✓ 模型前向传播: 输入{test_input.shape} -> 输出{output.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_quick_test():
    """运行快速测试"""
    print("\n=== 快速预测测试 ===")
    
    try:
        from q2_4090 import (
            load_station_data, prepare_features, get_train_test_split,
            persistence_model, evaluate_models_enhanced
        )
        
        # 加载少量数据进行测试
        df = load_station_data(1)
        df_sample = df.head(1000)  # 只使用前1000行数据
        
        print(f"✓ 使用样本数据: {df_sample.shape}")
        
        # 特征工程
        df_features = prepare_features(df_sample)
        
        # 数据划分
        train_indices, test_indices, test_month_data = get_train_test_split(df_features)
        
        if len(test_indices) == 0:
            print("✗ 测试集为空，可能是数据不足")
            return False
        
        X_train = df_features.loc[train_indices]
        X_test = df_features.loc[test_indices]
        
        print(f"✓ 数据划分完成: 训练集{len(X_train)}, 测试集{len(X_test)}")
        
        # 测试基准模型
        test_dates = sorted(X_test['date'].unique())
        persistence_pred = persistence_model(X_train, test_dates)
        
        print(f"✓ Persistence模型预测完成: {len(persistence_pred)}个预测值")
        
        # 评估
        y_test = X_test['power'].values
        predictions = {'Persistence': persistence_pred}
        is_daytime = X_test['is_daytime'].values
        
        results = evaluate_models_enhanced(y_test, predictions, is_daytime)
        print(f"✓ 模型评估完成")
        print(results)
        
        return True
        
    except Exception as e:
        print(f"✗ 快速测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("光伏发电功率预测 4090优化版 - 测试脚本")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("环境测试", test_environment),
        ("数据加载测试", test_data_loading),
        ("基本功能测试", test_basic_functions),
        ("模型创建测试", test_model_creation),
        ("快速预测测试", run_quick_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！可以运行完整程序。")
        print("运行命令: python q2_4090.py")
    else:
        print("⚠️  部分测试失败，请检查环境配置。")
    
    return passed == total

if __name__ == "__main__":
    main()
