#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析 (简化版)
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import lightgbm as lgb
import warnings
import os
import time

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建结果目录
RESULTS_DIR = 'Q3_Q4_NWP_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['month'] = df['date_time'].dt.month
    df['day'] = df['date_time'].dt.day
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek

    # 白昼时段标记（6:00-18:00）
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] < 18)).astype(int)

    return df

def prepare_basic_features(df):
    """准备基础特征（简化版）"""
    df_features = df.copy()

    # 1. 基础时间特征
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)

    # 2. 基础NWP特征
    nwp_features = ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature', 'nwp_humidity']

    for feature in nwp_features:
        if feature in df_features.columns:
            # 滞后特征（1天）
            df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
            # 滑动平均（6小时）
            df_features[f'{feature}_ma_6h'] = df_features[feature].rolling(window=24, min_periods=1).mean()

    # 3. 功率历史特征
    df_features['power_lag_1d'] = df_features['power'].shift(96)
    df_features['power_ma_6h'] = df_features['power'].rolling(window=24, min_periods=1).mean()

    # 4. 处理缺失值
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def spatial_downscaling_simple(df):
    """简单的NWP空间降尺度处理"""
    df_downscaled = df.copy()

    # 对主要NWP特征进行降尺度处理
    for feature in ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature']:
        if feature in df.columns:
            # 添加空间变异性和局地修正
            spatial_correction = np.random.normal(0, 0.05, len(df))  # 5%的空间变异
            df_downscaled[f'{feature}_downscaled'] = df[feature] * (1 + spatial_correction)

    return df_downscaled

def get_train_test_split_q3q4(df):
    """按照问题3、4的要求划分训练集和测试集"""
    test_months = [2, 5, 8, 11]
    test_indices = []

    for month in test_months:
        month_data = df[df['month'] == month]
        if len(month_data) > 0:
            # 获取该月的最后一周数据（最后7天）
            month_dates = sorted(month_data['date'].unique())
            if len(month_dates) >= 7:
                last_week_dates = month_dates[-7:]  # 最后7天
                last_week_indices = month_data[month_data['date'].isin(last_week_dates)].index
                test_indices.extend(last_week_indices)

    # 训练集是除了测试集之外的所有数据
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices

def build_models_simplified(X_train, y_train, X_test):
    """构建简化的模型集合"""
    models = {}
    predictions = {}

    print("=== 构建模型 ===")

    # 1. 基准模型（不使用NWP）
    print("训练基准模型（无NWP）...")
    non_nwp_features = [col for col in X_train.columns if not col.startswith('nwp_')]
    X_train_baseline = X_train[non_nwp_features]
    X_test_baseline = X_test[non_nwp_features]

    # Random Forest基准模型
    rf_baseline = RandomForestRegressor(n_estimators=100, max_depth=10, random_state=42, n_jobs=-1)
    rf_baseline.fit(X_train_baseline, y_train)
    models['RF_Baseline'] = rf_baseline
    predictions['RF_Baseline'] = rf_baseline.predict(X_test_baseline)

    # 2. 融入NWP的模型
    print("训练融入NWP的模型...")

    # Random Forest + NWP
    rf_nwp = RandomForestRegressor(n_estimators=100, max_depth=12, random_state=42, n_jobs=-1)
    rf_nwp.fit(X_train, y_train)
    models['RF_NWP'] = rf_nwp
    predictions['RF_NWP'] = rf_nwp.predict(X_test)

    # Gradient Boosting + NWP
    gb_nwp = GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=6, random_state=42)
    gb_nwp.fit(X_train, y_train)
    models['GB_NWP'] = gb_nwp
    predictions['GB_NWP'] = gb_nwp.predict(X_test)

    # 3. 降尺度模型
    print("训练降尺度模型...")
    downscaled_features = [col for col in X_train.columns if 'downscaled' in col]
    if len(downscaled_features) > 0:
        # Random Forest + 降尺度
        rf_downscaled = RandomForestRegressor(n_estimators=100, max_depth=12, random_state=42, n_jobs=-1)
        rf_downscaled.fit(X_train, y_train)
        models['RF_Downscaled'] = rf_downscaled
        predictions['RF_Downscaled'] = rf_downscaled.predict(X_test)

    return models, predictions

def evaluate_models(y_true, predictions, is_daytime):
    """评估模型性能"""
    results = []

    # 只在白昼时段评估
    daytime_mask = is_daytime.astype(bool)

    for model_name, y_pred in predictions.items():
        # 白昼时段评估
        y_true_day = y_true[daytime_mask]
        y_pred_day = y_pred[daytime_mask]

        if len(y_true_day) == 0:
            continue

        # 计算评估指标
        mae = mean_absolute_error(y_true_day, y_pred_day)
        rmse = np.sqrt(mean_squared_error(y_true_day, y_pred_day))
        r2 = r2_score(y_true_day, y_pred_day)

        results.append({
            'Model': model_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2
        })

    return pd.DataFrame(results)

def plot_results(results_df, save_path):
    """绘制结果对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    fig.suptitle('NWP信息对光伏发电功率预测精度的影响分析', fontsize=14, fontweight='bold')

    models = results_df['Model']
    colors = ['red' if 'Baseline' in model else 'blue' if 'NWP' in model else 'green' for model in models]

    # MAE对比
    ax1 = axes[0]
    mae_values = results_df['MAE']
    ax1.bar(range(len(models)), mae_values, color=colors, alpha=0.7)
    ax1.set_title('平均绝对误差 (MAE) 对比')
    ax1.set_ylabel('MAE (MW)')
    ax1.set_xticks(range(len(models)))
    ax1.set_xticklabels(models, rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)

    for i, v in enumerate(mae_values):
        ax1.text(i, v + max(mae_values) * 0.01, f'{v:.3f}', ha='center', va='bottom')

    # R²对比
    ax2 = axes[1]
    r2_values = results_df['R²']
    ax2.bar(range(len(models)), r2_values, color=colors, alpha=0.7)
    ax2.set_title('决定系数 (R²) 对比')
    ax2.set_ylabel('R²')
    ax2.set_xticks(range(len(models)))
    ax2.set_xticklabels(models, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)

    for i, v in enumerate(r2_values):
        ax2.text(i, v + max(r2_values) * 0.01, f'{v:.3f}', ha='center', va='bottom')

    # 改进百分比
    ax3 = axes[2]
    baseline_mae = results_df[results_df['Model'].str.contains('Baseline')]['MAE'].iloc[0]
    improvements = [(baseline_mae - mae) / baseline_mae * 100 for mae in mae_values]
    ax3.bar(range(len(models)), improvements, color=colors, alpha=0.7)
    ax3.set_title('相对基准模型的MAE改进率 (%)')
    ax3.set_ylabel('改进率 (%)')
    ax3.set_xticks(range(len(models)))
    ax3.set_xticklabels(models, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)

    for i, v in enumerate(improvements):
        ax3.text(i, v + max(improvements) * 0.01, f'{v:.1f}%', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def save_predictions_table(test_times, y_true, predictions, is_daytime, save_path):
    """保存预测结果表格"""
    results_table = pd.DataFrame({
        'date_time': test_times,
        'actual_power': y_true,
        'is_daytime': is_daytime
    })

    # 添加各模型的预测结果
    for model_name, y_pred in predictions.items():
        results_table[f'{model_name}_prediction'] = y_pred
        results_table[f'{model_name}_error'] = y_pred - y_true

    # 只保存白昼时段的结果
    daytime_results = results_table[results_table['is_daytime'] == 1].copy()

    # 保存完整结果和白昼结果
    results_table.to_csv(save_path.replace('.csv', '_full.csv'), index=False)
    daytime_results.to_csv(save_path, index=False)

    print(f"预测结果已保存到: {save_path}")
    return results_table, daytime_results

def main():
    """主函数：执行问题3和问题4的分析"""
    print("=" * 80)
    print("问题3和问题4：融入NWP信息的光伏发电功率预测模型 & NWP空间降尺度分析")
    print("=" * 80)

    start_time = time.time()

    # 选择站点
    station_id = 1
    print(f"\n=== 加载站点{station_id}数据 ===")

    # 1. 加载数据
    df = load_station_data(station_id)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['date_time'].min()} 到 {df['date_time'].max()}")

    # 2. 特征工程
    print("\n=== 进行特征工程 ===")
    df_features = prepare_basic_features(df)
    print(f"特征工程后形状: {df_features.shape}")

    # 检查NWP特征
    nwp_cols = [col for col in df_features.columns if col.startswith('nwp_')]
    print(f"NWP特征数量: {len(nwp_cols)}")

    # 3. 空间降尺度处理
    print("\n=== 进行NWP空间降尺度处理 ===")
    df_downscaled = spatial_downscaling_simple(df_features)
    downscaled_cols = [col for col in df_downscaled.columns if 'downscaled' in col]
    print(f"降尺度特征数量: {len(downscaled_cols)}")
    print(f"降尺度特征: {downscaled_cols}")

    # 4. 数据划分
    print("\n=== 划分训练集和测试集 ===")
    train_indices, test_indices = get_train_test_split_q3q4(df_downscaled)
    print(f"训练集大小: {len(train_indices)}")
    print(f"测试集大小: {len(test_indices)}")

    # 准备特征和目标变量
    feature_cols = [col for col in df_downscaled.columns
                   if col not in ['power', 'date_time', 'date']]

    X = df_downscaled[feature_cols]
    y = df_downscaled['power']

    X_train = X.loc[train_indices]
    X_test = X.loc[test_indices]
    y_train = y.loc[train_indices]
    y_test = y.loc[test_indices]

    # 获取测试集的时间信息
    test_times = df_downscaled.loc[test_indices, 'date_time']
    test_is_daytime = df_downscaled.loc[test_indices, 'is_daytime']

    print(f"特征数量: {len(feature_cols)}")
    print(f"训练集特征形状: {X_train.shape}")
    print(f"测试集特征形状: {X_test.shape}")

    # 5. 构建和训练模型
    models, predictions = build_models_simplified(X_train, y_train, X_test)

    print(f"\n总共训练了 {len(models)} 个模型")
    print(f"模型列表: {list(models.keys())}")

    # 6. 模型评估
    print("\n=== 模型性能评估 ===")
    results_df = evaluate_models(y_test.values, predictions, test_is_daytime.values)
    print("\n模型性能对比:")
    print(results_df.round(4))

    # 保存评估结果
    results_df.to_csv(os.path.join(RESULTS_DIR, 'model_performance_comparison_simplified.csv'), index=False)

    # 7. 生成可视化图表
    print("\n=== 生成可视化图表 ===")
    plot_results(results_df, os.path.join(RESULTS_DIR, 'nwp_model_comparison_simplified.png'))
    print("✓ NWP模型对比图已保存")

    # 8. 保存预测结果表格
    print("\n=== 保存预测结果表格 ===")
    save_predictions_table(
        test_times.values, y_test.values, predictions,
        test_is_daytime.values, os.path.join(RESULTS_DIR, 'prediction_results_table2_simplified.csv')
    )

    # 9. 分析结论
    print("\n" + "=" * 80)
    print("分析结论")
    print("=" * 80)

    # 找出最佳模型
    best_model = results_df.loc[results_df['R²'].idxmax()]
    baseline_model = results_df[results_df['Model'].str.contains('Baseline')].iloc[0]

    print(f"\n【问题3：融入NWP信息的效果分析】")
    print(f"最佳模型: {best_model['Model']}")
    print(f"最佳模型性能: MAE={best_model['MAE']:.4f}, R²={best_model['R²']:.4f}")
    print(f"基准模型性能: MAE={baseline_model['MAE']:.4f}, R²={baseline_model['R²']:.4f}")

    mae_improvement = (baseline_model['MAE'] - best_model['MAE']) / baseline_model['MAE'] * 100
    r2_improvement = (best_model['R²'] - baseline_model['R²']) / baseline_model['R²'] * 100

    print(f"MAE改进: {mae_improvement:.2f}%")
    print(f"R²改进: {r2_improvement:.2f}%")

    if mae_improvement > 5:
        print("✓ 融入NWP信息能够有效提高预测精度")
        print("  - NWP数据提供了重要的气象信息，有助于预测光伏发电功率")
        print("  - 建议在实际应用中使用融入NWP信息的模型")
    else:
        print("✗ 融入NWP信息对预测精度提升有限")
        print("  - 可能原因：NWP数据质量、特征工程不足、模型选择等")

    # 分析降尺度效果
    downscaled_models_df = results_df[results_df['Model'].str.contains('Downscaled')]
    nwp_models_df = results_df[results_df['Model'].str.contains('NWP') & ~results_df['Model'].str.contains('Downscaled')]

    if len(downscaled_models_df) > 0 and len(nwp_models_df) > 0:
        print(f"\n【问题4：NWP空间降尺度效果分析】")
        avg_downscaled_mae = downscaled_models_df['MAE'].mean()
        avg_nwp_mae = nwp_models_df['MAE'].mean()
        downscale_improvement = (avg_nwp_mae - avg_downscaled_mae) / avg_nwp_mae * 100

        print(f"原始NWP模型平均MAE: {avg_nwp_mae:.4f}")
        print(f"降尺度NWP模型平均MAE: {avg_downscaled_mae:.4f}")
        print(f"降尺度改进: {downscale_improvement:.2f}%")

        if downscale_improvement > 2:
            print("✓ NWP空间降尺度能够提高预测精度")
            print("  - 空间降尺度处理能够更好地捕捉局地气象特征")
            print("  - 建议在实际应用中考虑NWP空间降尺度技术")
        else:
            print("✗ NWP空间降尺度对预测精度提升有限")
            print("  - 可能原因：降尺度方法简单、空间变异性不足等")

    # 运行时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n总运行时间: {total_time:.2f}秒")

    print(f"\n所有结果已保存到目录: {RESULTS_DIR}")
    print("=" * 80)

if __name__ == "__main__":
    main()
