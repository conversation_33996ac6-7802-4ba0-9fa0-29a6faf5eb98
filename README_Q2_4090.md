# 光伏电站发电功率日前预测 - RTX 4090优化版

## 概述

本项目是专门针对NVIDIA RTX 4090显卡优化的光伏电站发电功率日前预测系统。相比原版本，该版本在以下方面进行了优化：

### 🚀 4090显卡优化特性

1. **GPU加速训练**
   - LightGBM GPU训练支持
   - PyTorch混合精度训练 (AMP)
   - 优化的批次大小和内存管理
   - 多线程数据加载

2. **增强的深度学习模型**
   - 高级TCN (Temporal Convolutional Network) 模型
   - 多头注意力机制
   - 批量归一化和Dropout正则化
   - 学习率调度和早停机制

3. **丰富的特征工程**
   - 更多滞后特征 (1天、2天、7天)
   - 扩展的滑动窗口统计
   - 气象特征交互项
   - 周期性时间特征

4. **全面的评估和可视化**
   - R²决定系数评估
   - 分月预测结果展示
   - 模型性能雷达图
   - 误差分布分析
   - 残差时间序列分析

## 📋 系统要求

### 硬件要求
- NVIDIA RTX 4090 显卡 (推荐)
- 至少16GB系统内存
- 至少10GB GPU显存

### 软件要求
- Python 3.8+
- CUDA 11.8+ (推荐)
- cuDNN 8.0+

### 依赖包
```bash
pip install pandas numpy matplotlib seaborn scikit-learn
pip install lightgbm xgboost torch torchvision torchaudio
pip install statsmodels pmdarima prophet tqdm shap
```

## 🗂️ 文件结构

```
├── q2_4090.py              # 主程序文件
├── run_q2_4090.py          # 运行脚本
├── README_Q2_4090.md       # 说明文档
├── dataset/                # 数据目录
│   ├── station01.csv       # 站点1数据
│   ├── station02.csv       # 站点2数据
│   └── ...
└── Q2_4090_results/        # 结果输出目录
    ├── model_evaluation_4090.csv
    ├── prediction_comparison_4090.png
    ├── prediction_month_*.png
    ├── model_performance_radar.png
    ├── feature_importance/
    └── ...
```

## 🚀 使用方法

### 1. 直接运行
```bash
python q2_4090.py
```

### 2. 使用运行脚本
```bash
python run_q2_4090.py
```

### 3. 自定义参数
可以修改 `q2_4090.py` 中的以下参数：
- `station_id`: 选择不同的站点 (1-10)
- `BATCH_SIZE`: 调整批次大小
- `NUM_WORKERS`: 调整工作线程数

## 📊 输出结果

### 1. 性能评估文件
- `model_evaluation_4090.csv`: 模型性能指标
- `model_improvement_comparison.csv`: 相对基准的改进
- `model_ranking.csv`: 模型排名

### 2. 预测结果文件
- `station1_prediction_results.csv`: 详细预测结果
- `station1_analysis_report.md`: 分析报告

### 3. 可视化图表
- `prediction_comparison_4090.png`: 总体预测对比
- `prediction_month_*.png`: 分月预测结果
- `model_performance_radar.png`: 性能雷达图
- `error_distribution_boxplot.png`: 误差分布
- `scatter_plot_matrix.png`: 散点图矩阵
- `residual_time_series.png`: 残差时间序列

### 4. 特征重要性分析
- `feature_importance/lgbm_feature_importance.png`
- `feature_importance/lgbm_feature_importance.csv`

## 🎯 模型特点

### 1. Persistence基准模型
- 使用前一天同时刻功率作为预测值
- 作为其他模型的性能基准

### 2. LightGBM模型 (4090优化)
- GPU加速训练
- 增强的超参数设置
- 特征重要性分析

### 3. 高级TCN深度学习模型
- 时间卷积网络架构
- 多头注意力机制
- 混合精度训练
- 自适应学习率调度

## 📈 性能优化

### 4090显卡优化设置
```python
# 批次大小优化
BATCH_SIZE = 512  # 充分利用4090显存

# 混合精度训练
torch.cuda.amp.autocast()
torch.cuda.amp.GradScaler()

# GPU内存优化
torch.backends.cudnn.benchmark = True
pin_memory = True
```

### 训练加速技巧
1. **数据预处理并行化**: 使用多线程数据加载
2. **模型缓存**: 训练好的模型自动保存和加载
3. **早停机制**: 避免过拟合和节省时间
4. **学习率调度**: 自适应调整学习率

## 📋 评估指标

本系统使用以下指标评估模型性能：

1. **MAE** (平均绝对误差): 预测精度指标
2. **RMSE** (均方根误差): 预测稳定性指标
3. **nRMSE** (归一化RMSE): 相对误差指标
4. **SMAPE** (对称平均绝对百分比误差): 百分比误差
5. **R²** (决定系数): 模型拟合度指标

## ⚠️ 注意事项

1. **数据要求**: 确保数据文件格式正确，包含所需的时间戳和功率列
2. **GPU内存**: 如果遇到GPU内存不足，可以减小BATCH_SIZE
3. **训练时间**: 深度学习模型训练时间较长，建议在有充足时间时运行
4. **白昼评估**: 所有指标仅在白昼时段(6:00-18:00)计算

## 🔧 故障排除

### 常见问题

1. **CUDA不可用**
   ```
   解决方案: 检查CUDA安装，确保PyTorch支持CUDA
   ```

2. **GPU内存不足**
   ```python
   # 减小批次大小
   BATCH_SIZE = 256  # 或更小
   ```

3. **依赖包缺失**
   ```bash
   pip install -r requirements.txt
   ```

## 📞 技术支持

如有问题，请检查：
1. 数据文件是否存在且格式正确
2. GPU驱动和CUDA是否正确安装
3. 所有依赖包是否已安装

## 🎉 预期效果

使用4090优化版本，您可以期待：
- **更快的训练速度**: GPU加速训练比CPU快5-10倍
- **更高的预测精度**: 高级模型架构提升预测性能
- **更丰富的分析**: 全面的可视化和评估报告
- **更好的用户体验**: 自动化的结果生成和保存
