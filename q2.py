import os
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
import pmdarima as pm
from prophet import Prophet
from sklearn.metrics import mean_absolute_error
from tqdm import tqdm
import pickle
import time
import lightgbm as lgb
from xgboost import XGBRegressor
from sklearn.preprocessing import StandardScaler
import shap
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.ensemble import StackingRegressor
from sklearn.linear_model import LinearRegression, Ridge

# 设置中文字体和图表风格
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

# 创建结果目录
RESULTS_DIR = 'Q2_results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 数据处理函数
def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    
    # 添加时间相关特征
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['day'] = df['date_time'].dt.day
    df['month'] = df['date_time'].dt.month
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek
    df['date'] = df['date_time'].dt.date
    
    # 添加时间段标记（96个时间点）
    df['time_point'] = df['hour'] * 4 + df['minute'] // 15
    
    return df

def prepare_features(df, lag_days=[1, 2], window_sizes=[12, 96]):
    """准备预测特征
    
    Args:
        df: 包含功率数据的DataFrame
        lag_days: 滞后天数列表
        window_sizes: 滑动窗口大小列表
    """
    # 复制数据，避免修改原始数据
    df_features = df.copy()
    
    # 1. 滞后功率特征 (t-96: 前1天同时刻, t-192: 前2天同时刻)
    for day in lag_days:
        lag_points = day * 96  # 每天96个时间点
        df_features[f'power_lag_{day}d'] = df_features['power'].shift(lag_points)
    
    # 2. 滑动统计特征
    for window in window_sizes:
        # 滑动平均
        df_features[f'power_mean_{window}'] = df_features['power'].rolling(window=window, min_periods=1).mean()
        # 滑动标准差
        df_features[f'power_std_{window}'] = df_features['power'].rolling(window=window, min_periods=1).std()
    
    # 3. 季节性特征
    # 小时的周期性（一天内的周期）
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    
    # 月份的周期性（一年内的周期）
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    
    # 4. 一周中的天
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)
    
    # 5. 相对理论效率特征（使用NWP全球辐照度数据近似）
    if 'nwp_globalirrad' in df_features.columns:
        # 使用简化的理论模型：理论功率 = 辐照度 * 常数
        df_features['theoretical_power'] = df_features['nwp_globalirrad'] * 0.85 / 1000
        # 避免除以0
        df_features['relative_efficiency'] = np.where(
            df_features['theoretical_power'] > 0,
            df_features['power'] / df_features['theoretical_power'],
            np.nan
        )
        df_features['relative_efficiency_lag_1d'] = df_features['relative_efficiency'].shift(96)
    
    # 6. 白天/夜间标记
    df_features['is_daytime'] = ((df_features['hour'] >= 6) & (df_features['hour'] <= 18)).astype(int)
    
    # 7. 处理缺失值
    df_features['missing_flag'] = df_features['power'].isna().astype(int)
    
    # 填充缺失值，避免训练问题
    # 注意：实际预测中可能需要更复杂的缺失值处理
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())
    
    return df_features

def get_train_test_split(df, test_months=[2, 5, 8, 11]):
    """按照题目要求划分训练集和测试集
    
    训练集：除2/5/8/11月最后一周外的数据
    测试集：2/5/8/11月最后一周数据
    """
    # 先按月份筛选出测试月份
    test_month_df = df[df['month'].isin(test_months)]
    
    # 对于每个测试月份，找出最后一周
    test_indices = []
    for month in test_months:
        month_df = df[df['month'] == month]
        if len(month_df) == 0:
            continue
            
        # 获取该月最大日期
        max_date = month_df['date'].max()
        
        # 获取最大日期前7天的数据作为测试集
        test_dates = [(max_date - timedelta(days=i)) for i in range(7)]
        month_test_indices = df[df['date'].isin(test_dates)].index
        test_indices.extend(month_test_indices)
    
    # 剩余数据作为训练集
    train_indices = df.index.difference(test_indices)
    
    return train_indices, test_indices

def plot_prediction_comparison(y_true, predictions, title, save_path):
    """绘制不同模型的预测效果对比"""
    plt.figure(figsize=(12, 6))
    
    # 绘制真实值
    plt.plot(y_true.values, 'k-', label='真实值', linewidth=2)
    
    # 绘制不同模型的预测值
    for model_name, y_pred in predictions.items():
        plt.plot(y_pred, '--', label=f'{model_name}预测值', linewidth=1.5, alpha=0.8)
    
    plt.title(title, fontsize=14)
    plt.xlabel('时间点', fontsize=12)
    plt.ylabel('功率 (MW)', fontsize=12)
    plt.legend(loc='best')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(save_path)
    plt.close()

def evaluate_models(y_true, predictions):
    """评估不同模型的预测效果"""
    results = {}
    
    for model_name, y_pred in predictions.items():
        # 计算平均绝对误差 (MAE)
        mae = np.mean(np.abs(y_true - y_pred))
        
        # 计算均方根误差 (RMSE)
        rmse = np.sqrt(np.mean((y_true - y_pred) ** 2))
        
        # 计算归一化均方根误差 (nRMSE)
        nrmse = rmse / np.mean(y_true)
        
        # 计算对称平均绝对百分比误差 (SMAPE)
        smape = 100 * np.mean(2 * np.abs(y_pred - y_true) / (np.abs(y_pred) + np.abs(y_true) + 1e-8))
        
        results[model_name] = {
            'MAE': mae,
            'RMSE': rmse,
            'nRMSE': nrmse,
            'SMAPE': smape
        }
    
    # 转换为DataFrame以便于展示
    results_df = pd.DataFrame(results).T
    return results_df

def persistence_model(train_data, test_dates, time_points=96):
    """
    实现Persistence模型（基准线）
    使用前一天同一时刻的功率作为预测值
    
    Args:
        train_data: 训练数据
        test_dates: 测试日期列表
        time_points: 每天的时间点数量
    
    Returns:
        预测结果DataFrame
    """
    print("运行Persistence基准模型...")
    predictions = []
    
    # 对每个测试日期进行预测
    for test_date in test_dates:
        # 获取前一天的日期
        prev_day = test_date - timedelta(days=1)
        
        # 获取前一天的功率值
        prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
        
        if len(prev_day_powers) == time_points:
            # 如果前一天有完整的数据，直接使用
            predictions.extend(prev_day_powers)
        else:
            # 如果前一天数据不完整，使用最近的有效日期
            for day_offset in range(2, 8):  # 尝试前2~7天
                prev_day = test_date - timedelta(days=day_offset)
                prev_day_powers = train_data[train_data['date'] == prev_day]['power'].values
                if len(prev_day_powers) == time_points:
                    predictions.extend(prev_day_powers)
                    break
            else:
                # 如果前7天都没有完整数据，使用训练集中同时刻的平均值
                for time_point in range(time_points):
                    time_point_avg = train_data[train_data['time_point'] == time_point]['power'].mean()
                    predictions.append(time_point_avg)
    
    return np.array(predictions)

def arima_model(train_data, test_dates, time_point_count=96):
    """
    使用ARIMA模型进行预测
    对每个时间点单独建模
    
    Args:
        train_data: 训练数据
        test_dates: 测试日期列表
        time_point_count: 每天的时间点数量
    
    Returns:
        预测结果DataFrame
    """
    print("运行ARIMA模型...")
    
    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'arima_models')
    os.makedirs(models_dir, exist_ok=True)
    
    predictions = []
    
    # 按时间点分别进行预测
    for time_point in tqdm(range(time_point_count), desc="ARIMA模型训练"):
        # 提取该时间点的历史数据
        time_series = train_data[train_data['time_point'] == time_point]['power']
        
        # 如果模型已存在，直接加载
        model_path = os.path.join(models_dir, f'arima_model_tp{time_point}.pkl')
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
        else:
            # 使用pmdarima自动确定最佳参数
            try:
                model = pm.auto_arima(time_series, 
                                     start_p=1, start_q=1,
                                     max_p=3, max_q=3,
                                     m=7,  # 一周7天的季节性
                                     seasonal=True,
                                     d=1, D=1,
                                     trace=False,
                                     error_action='ignore',
                                     suppress_warnings=True,
                                     stepwise=True)
                
                # 保存模型
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
            except Exception as e:
                print(f"时间点{time_point}的ARIMA模型训练失败: {e}")
                # 如果模型训练失败，使用简单的历史平均值
                predictions.append(time_series.mean())
                continue
        
        # 进行预测
        try:
            forecast = model.predict(n_periods=len(test_dates))
            predictions.extend(forecast)
        except Exception as e:
            print(f"时间点{time_point}的预测失败: {e}")
            # 如果预测失败，使用历史平均值
            for _ in range(len(test_dates)):
                predictions.append(time_series.mean())
    
    return np.array(predictions)

def prophet_model(train_data, test_dates, time_point_count=96):
    """
    使用Prophet模型进行预测
    
    Args:
        train_data: 训练数据
        test_dates: 测试日期列表
        time_point_count: 每天的时间点数量
    
    Returns:
        预测结果数组
    """
    print("运行Prophet模型...")
    
    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'prophet_models')
    os.makedirs(models_dir, exist_ok=True)
    
    predictions = []
    
    # 按时间点分别进行预测
    for time_point in tqdm(range(time_point_count), desc="Prophet模型训练"):
        # 提取该时间点的历史数据
        time_point_data = train_data[train_data['time_point'] == time_point][['date_time', 'power']]
        
        # 重命名列以适应Prophet的要求
        prophet_df = time_point_data.rename(columns={'date_time': 'ds', 'power': 'y'})
        
        # 如果模型已存在，直接加载
        model_path = os.path.join(models_dir, f'prophet_model_tp{time_point}.pkl')
        if os.path.exists(model_path):
            with open(model_path, 'rb') as f:
                model = pickle.load(f)
        else:
            # 创建并训练Prophet模型
            try:
                model = Prophet(
                    yearly_seasonality=True,
                    weekly_seasonality=True,
                    daily_seasonality=False,
                    seasonality_mode='multiplicative',
                    interval_width=0.95
                )
                model.fit(prophet_df)
                
                # 保存模型
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)
            except Exception as e:
                print(f"时间点{time_point}的Prophet模型训练失败: {e}")
                # 如果模型训练失败，使用简单的历史平均值
                predictions.append(prophet_df['y'].mean() * len(test_dates))
                continue
        
        # 创建预测日期DataFrame
        future_dates = [datetime.combine(date, datetime.min.time()) + timedelta(hours=int(time_point/4), minutes=(time_point%4)*15) 
                       for date in test_dates]
        future = pd.DataFrame({'ds': future_dates})
        
        # 进行预测
        try:
            forecast = model.predict(future)
            predictions.extend(forecast['yhat'].values)
        except Exception as e:
            print(f"时间点{time_point}的预测失败: {e}")
            # 如果预测失败，使用历史平均值
            for _ in range(len(test_dates)):
                predictions.append(prophet_df['y'].mean())
    
    return np.array(predictions)

def prepare_data_for_next_day_prediction(df_features, test_dates):
    """
    准备用于日前预测的数据
    
    Args:
        df_features: 特征数据
        test_dates: 测试日期列表
    
    Returns:
        测试集X和y
    """
    # 筛选测试数据
    test_data = df_features[df_features['date'].isin(test_dates)]
    
    # 保存真实值用于评估
    y_test = test_data['power'].values
    
    # 用于预测的特征
    feature_cols = [
        'power_lag_1d', 'power_lag_2d',  # 滞后特征
        'power_mean_12', 'power_std_96',  # 滑动统计
        'hour_sin', 'hour_cos',           # 时间特征
        'month_sin', 'month_cos',
        'dayofweek_sin', 'dayofweek_cos',
        'is_daytime'                      # 白天/夜间标记
    ]
    
    if 'relative_efficiency_lag_1d' in test_data.columns:
        feature_cols.append('relative_efficiency_lag_1d')
    
    X_test = test_data[feature_cols]
    
    return X_test, y_test, test_data

def lgbm_model(X_train, y_train, X_test, test_dates):
    """
    使用LightGBM模型进行预测
    针对每个时间点（96点）分别建模预测
    
    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        test_dates: 测试日期列表
    
    Returns:
        预测结果数组和特征重要性
    """
    print("运行LightGBM模型...")
    
    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'lgbm_models')
    os.makedirs(models_dir, exist_ok=True)
    
    # 保存特征重要性的目录
    feature_imp_dir = os.path.join(RESULTS_DIR, 'feature_importance')
    os.makedirs(feature_imp_dir, exist_ok=True)
    
    # 初始化结果和特征重要性
    predictions = np.zeros(len(X_test))
    feature_importance = {}
    
    # 按时间点分组训练和预测
    for time_point in tqdm(range(96), desc="LightGBM模型训练"):
        # 筛选该时间点的训练数据
        train_mask = (X_train['time_point'] == time_point)
        if train_mask.sum() == 0:
            continue
            
        X_train_tp = X_train[train_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
        y_train_tp = y_train[train_mask]
        
        # 筛选该时间点的测试数据
        test_mask = (X_test['time_point'] == time_point)
        X_test_tp = X_test[test_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
        
        # 模型路径
        model_path = os.path.join(models_dir, f'lgbm_model_tp{time_point}.txt')
        
        if os.path.exists(model_path):
            # 如果模型已存在，直接加载
            model = lgb.Booster(model_file=model_path)
        else:
            # LightGBM参数
            params = {
                'boosting_type': 'gbdt',
                'objective': 'regression',
                'metric': 'mae',
                'learning_rate': 0.05,
                'num_leaves': 31,
                'max_depth': 8,
                'feature_fraction': 0.8,
                'bagging_fraction': 0.8,
                'bagging_freq': 5,
                'verbose': -1
            }
            
            # 创建数据集
            train_data = lgb.Dataset(X_train_tp, label=y_train_tp)
            
            # 训练模型
            model = lgb.train(
                params,
                train_data,
                num_boost_round=1000,
                early_stopping_rounds=50,
                valid_sets=[train_data],
                verbose_eval=False
            )
            
            # 保存模型
            model.save_model(model_path)
        
        # 预测
        if len(X_test_tp) > 0:
            preds = model.predict(X_test_tp)
            predictions[test_mask] = preds
        
        # 获取特征重要性
        importances = model.feature_importance(importance_type='gain')
        feature_names = model.feature_name()
        
        # 累加每个时间点的特征重要性
        for feat_name, importance in zip(feature_names, importances):
            if feat_name in feature_importance:
                feature_importance[feat_name] += importance
            else:
                feature_importance[feat_name] = importance
    
    # 绘制特征重要性图
    plot_feature_importance(feature_importance, 'LightGBM', os.path.join(feature_imp_dir, 'lgbm_feature_importance.png'))
    
    # 保存特征重要性数据
    pd.DataFrame({
        'Feature': list(feature_importance.keys()),
        'Importance': list(feature_importance.values())
    }).sort_values(by='Importance', ascending=False).to_csv(
        os.path.join(feature_imp_dir, 'lgbm_feature_importance.csv'), index=False
    )
    
    # SHAP值分析
    try:
        # 选择一个代表性时间点的模型进行SHAP分析
        time_point = 36  # 通常白天，有足够的光照
        model_path = os.path.join(models_dir, f'lgbm_model_tp{time_point}.txt')
        if os.path.exists(model_path):
            model = lgb.Booster(model_file=model_path)
            
            # 筛选该时间点的数据
            train_mask = (X_train['time_point'] == time_point)
            X_train_tp = X_train[train_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
            
            # 计算SHAP值
            explainer = shap.TreeExplainer(model)
            shap_values = explainer.shap_values(X_train_tp.iloc[:100])  # 使用前100个样本进行分析
            
            # 保存SHAP摘要图
            plt.figure(figsize=(10, 8))
            shap.summary_plot(shap_values, X_train_tp.iloc[:100], show=False)
            plt.title(f"LightGBM SHAP值分析 (时间点 {time_point})")
            plt.tight_layout()
            plt.savefig(os.path.join(feature_imp_dir, 'lgbm_shap_summary.png'))
            plt.close()
    except Exception as e:
        print(f"SHAP分析出错: {e}")
    
    return predictions, feature_importance

def xgboost_model(X_train, y_train, X_test):
    """
    使用XGBoost模型进行预测
    
    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
    
    Returns:
        预测结果数组和特征重要性
    """
    print("运行XGBoost模型...")
    
    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'xgb_models')
    os.makedirs(models_dir, exist_ok=True)
    
    # 初始化结果和特征重要性
    predictions = np.zeros(len(X_test))
    feature_importance = {}
    
    # 按时间点分组训练和预测
    for time_point in tqdm(range(96), desc="XGBoost模型训练"):
        # 筛选该时间点的训练数据
        train_mask = (X_train['time_point'] == time_point)
        if train_mask.sum() == 0:
            continue
            
        X_train_tp = X_train[train_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
        y_train_tp = y_train[train_mask]
        
        # 筛选该时间点的测试数据
        test_mask = (X_test['time_point'] == time_point)
        X_test_tp = X_test[test_mask].drop(['time_point', 'date', 'date_time'], axis=1, errors='ignore')
        
        # 模型路径
        model_path = os.path.join(models_dir, f'xgb_model_tp{time_point}.json')
        
        if os.path.exists(model_path):
            # 如果模型已存在，直接加载
            model = XGBRegressor()
            model.load_model(model_path)
        else:
            # 创建XGBoost模型
            model = XGBRegressor(
                n_estimators=1000,
                learning_rate=0.05,
                max_depth=6,
                min_child_weight=1,
                subsample=0.8,
                colsample_bytree=0.8,
                gamma=0,
                objective='reg:squarederror',
                random_state=42,
                early_stopping_rounds=50
            )
            
            # 训练模型
            model.fit(
                X_train_tp, y_train_tp,
                eval_set=[(X_train_tp, y_train_tp)],
                verbose=False
            )
            
            # 保存模型
            model.save_model(model_path)
        
        # 预测
        if len(X_test_tp) > 0:
            preds = model.predict(X_test_tp)
            predictions[test_mask] = preds
        
        # 获取特征重要性
        for feat_name, importance in zip(X_train_tp.columns, model.feature_importances_):
            if feat_name in feature_importance:
                feature_importance[feat_name] += importance
            else:
                feature_importance[feat_name] = importance
    
    # 保存特征重要性
    feature_imp_dir = os.path.join(RESULTS_DIR, 'feature_importance')
    os.makedirs(feature_imp_dir, exist_ok=True)
    
    # 绘制特征重要性图
    plot_feature_importance(feature_importance, 'XGBoost', os.path.join(feature_imp_dir, 'xgb_feature_importance.png'))
    
    # 保存特征重要性数据
    pd.DataFrame({
        'Feature': list(feature_importance.keys()),
        'Importance': list(feature_importance.values())
    }).sort_values(by='Importance', ascending=False).to_csv(
        os.path.join(feature_imp_dir, 'xgb_feature_importance.csv'), index=False
    )
    
    return predictions, feature_importance

def plot_feature_importance(feature_importance, model_name, save_path):
    """
    绘制特征重要性图
    """
    # 转换为DataFrame
    feat_imp = pd.DataFrame({
        'Feature': list(feature_importance.keys()),
        'Importance': list(feature_importance.values())
    }).sort_values(by='Importance', ascending=False).head(15)  # 只显示前15个特征
    
    # 绘图
    plt.figure(figsize=(10, 6))
    sns.barplot(x='Importance', y='Feature', data=feat_imp)
    plt.title(f'{model_name}模型特征重要性', fontsize=14)
    plt.xlabel('重要性', fontsize=12)
    plt.ylabel('特征', fontsize=12)
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    
    plt.savefig(save_path)
    plt.close()

# 添加TCN模型类
class TemporalBlock(nn.Module):
    def __init__(self, n_inputs, n_outputs, kernel_size, stride, dilation, padding, dropout=0.2):
        super(TemporalBlock, self).__init__()
        self.conv1 = nn.Conv1d(n_inputs, n_outputs, kernel_size,
                               stride=stride, padding=padding, dilation=dilation)
        self.conv2 = nn.Conv1d(n_outputs, n_outputs, kernel_size,
                               stride=stride, padding=padding, dilation=dilation)
        self.net = nn.Sequential(
            self.conv1,
            nn.ReLU(),
            nn.Dropout(dropout),
            self.conv2,
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        self.downsample = nn.Conv1d(n_inputs, n_outputs, 1) if n_inputs != n_outputs else None
        self.relu = nn.ReLU()
        self.init_weights()
        
    def init_weights(self):
        self.conv1.weight.data.normal_(0, 0.01)
        self.conv2.weight.data.normal_(0, 0.01)
        if self.downsample is not None:
            self.downsample.weight.data.normal_(0, 0.01)
    
    def forward(self, x):
        out = self.net(x)
        res = x if self.downsample is None else self.downsample(x)
        return self.relu(out + res)


class TemporalConvNet(nn.Module):
    def __init__(self, num_inputs, num_channels, kernel_size=2, dropout=0.2):
        super(TemporalConvNet, self).__init__()
        layers = []
        num_levels = len(num_channels)
        for i in range(num_levels):
            dilation_size = 2 ** i
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]
            padding = (kernel_size-1) * dilation_size
            layers += [TemporalBlock(in_channels, out_channels, kernel_size, stride=1,
                                     dilation=dilation_size, padding=padding, dropout=dropout)]
        
        self.network = nn.Sequential(*layers)
    
    def forward(self, x):
        return self.network(x)


class TCN(nn.Module):
    def __init__(self, input_size, output_size, num_channels, kernel_size, dropout):
        super(TCN, self).__init__()
        self.tcn = TemporalConvNet(input_size, num_channels, kernel_size=kernel_size, dropout=dropout)
        self.linear = nn.Linear(num_channels[-1], output_size)
    
    def forward(self, x):
        # x shape: [batch, seq_len, input_size]
        # TCN需要的输入形状: [batch, input_size, seq_len]
        x = x.permute(0, 2, 1)
        y1 = self.tcn(x)
        # 只取最后一个时间步的输出
        y2 = y1[:, :, -1]
        return self.linear(y2)


class TimeSeriesDataset(Dataset):
    def __init__(self, X, y, seq_length=12):
        self.X = X
        self.y = y
        self.seq_length = seq_length
    
    def __len__(self):
        return len(self.X) - self.seq_length + 1
    
    def __getitem__(self, idx):
        X_seq = self.X[idx:idx+self.seq_length]
        y_target = self.y[idx+self.seq_length-1]
        return torch.FloatTensor(X_seq), torch.FloatTensor([y_target])


def tcn_model(X_train, y_train, X_test, test_dates):
    """
    使用TCN (Temporal Convolutional Network)模型进行预测
    
    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        test_dates: 测试日期列表
    
    Returns:
        预测结果数组
    """
    print("运行TCN深度学习模型...")
    
    # 创建存储模型的目录
    models_dir = os.path.join(RESULTS_DIR, 'tcn_models')
    os.makedirs(models_dir, exist_ok=True)
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 初始化结果
    predictions = np.zeros(len(X_test))
    
    # 特征列
    feature_cols = [
        'power_lag_1d', 'power_lag_2d',  # 滞后特征
        'power_mean_12', 'power_std_96',  # 滑动统计
        'hour_sin', 'hour_cos',           # 时间特征
        'month_sin', 'month_cos',
        'dayofweek_sin', 'dayofweek_cos',
        'is_daytime'                      # 白天/夜间标记
    ]
    
    # 数据标准化
    scaler_X = StandardScaler()
    scaler_y = StandardScaler()
    
    # 按时间点训练和预测
    for time_point in tqdm(range(96), desc="TCN模型训练"):
        # 筛选该时间点的训练数据
        train_mask = (X_train['time_point'] == time_point)
        if train_mask.sum() <= 12:  # 需要足够的序列长度
            continue
            
        # 筛选特征
        X_train_tp = X_train.loc[train_mask, feature_cols].values
        y_train_tp = y_train[train_mask]
        
        # 标准化数据
        X_train_scaled = scaler_X.fit_transform(X_train_tp)
        y_train_scaled = scaler_y.fit_transform(y_train_tp.reshape(-1, 1)).flatten()
        
        # 筛选该时间点的测试数据
        test_mask = (X_test['time_point'] == time_point)
        X_test_tp = X_test.loc[test_mask, feature_cols].values
        
        # 如果测试集为空，跳过
        if len(X_test_tp) == 0:
            continue
        
        # 标准化测试数据
        X_test_scaled = scaler_X.transform(X_test_tp)
        
        # 模型路径
        model_path = os.path.join(models_dir, f'tcn_model_tp{time_point}.pth')
        
        if os.path.exists(model_path) and False:  # 暂时不使用已保存的模型，以便每次运行都完整训练
            # 如果模型已存在，直接加载
            model = TCN(
                input_size=len(feature_cols),
                output_size=1,
                num_channels=[32, 32, 16],
                kernel_size=3,
                dropout=0.2
            )
            model.load_state_dict(torch.load(model_path, map_location=device))
            model.to(device)
            model.eval()
        else:
            # 创建数据集和数据加载器
            seq_length = 12  # 使用12个时间步作为输入序列
            train_dataset = TimeSeriesDataset(X_train_scaled, y_train_scaled, seq_length)
            train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)
            
            # 创建模型
            model = TCN(
                input_size=len(feature_cols),
                output_size=1,
                num_channels=[32, 32, 16],
                kernel_size=3,
                dropout=0.2
            )
            model.to(device)
            
            # 训练参数
            criterion = nn.MSELoss()
            optimizer = optim.Adam(model.parameters(), lr=0.001)
            epochs = 50
            
            # 训练
            model.train()
            for epoch in range(epochs):
                running_loss = 0.0
                for batch_X, batch_y in train_loader:
                    batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                    
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    
                    running_loss += loss.item()
                
                if (epoch + 1) % 10 == 0:
                    print(f"  Epoch {epoch+1}, Loss: {running_loss/len(train_loader):.6f}")
            
            # 保存模型
            torch.save(model.state_dict(), model_path)
        
        # 预测
        model.eval()
        with torch.no_grad():
            for i in range(len(X_test_tp)):
                # 构建输入序列
                # 通常会使用历史数据作为输入序列，但这里简化为重复当前样本
                seq = np.repeat(X_test_scaled[i:i+1], seq_length, axis=0)
                seq_tensor = torch.FloatTensor(seq).unsqueeze(0).to(device)  # [1, seq_len, features]
                
                pred = model(seq_tensor)
                pred_value = pred.item()
                
                # 反标准化
                pred_value = scaler_y.inverse_transform([[pred_value]])[0][0]
                
                # 存储预测结果
                predictions[test_mask.to_numpy().nonzero()[0][i]] = max(0, pred_value)  # 确保功率非负
    
    return predictions


def ensemble_model(predictions_dict):
    """
    实现简单的模型集成（加权平均）
    
    Args:
        predictions_dict: 各模型的预测结果字典
    
    Returns:
        集成后的预测结果
    """
    print("执行模型集成...")
    
    # 首先将所有预测结果转换为数组格式
    pred_array = np.array([pred for pred in predictions_dict.values()])
    
    # 简单平均集成
    ensemble_avg = np.mean(pred_array, axis=0)
    
    # 加权平均集成（基于模型性能的权重）
    # 这里可以根据模型在验证集上的表现分配权重
    # 暂时使用一个简单的权重设置（可以根据需要调整）
    weights = {
        'Persistence': 0.05,
        'ARIMA': 0.15,
        'Prophet': 0.15,
        'LightGBM': 0.35,
        'XGBoost': 0.30,
        'TCN': 0.00  # 默认权重为0，后面会根据是否有TCN预测结果进行调整
    }
    
    # 动态调整权重
    if 'TCN' in predictions_dict:
        weights['TCN'] = 0.2
        # 重新归一化其他权重
        total = sum(weights.values())
        for k in weights:
            weights[k] /= total
    
    # 计算加权平均结果
    ensemble_weighted = np.zeros_like(ensemble_avg)
    for model, pred in predictions_dict.items():
        if model in weights:
            ensemble_weighted += weights[model] * pred
    
    return ensemble_avg, ensemble_weighted

# 主函数
if __name__ == "__main__":
    print("正在执行光伏电站日前发电功率预测（第二问）...")
    
    # 选择一个站点进行分析，可以根据需要修改
    station_id = 1
    
    # 加载数据
    df = load_station_data(station_id)
    print(f"成功加载站点{station_id}数据，共{len(df)}条记录")
    
    # 准备特征
    df_features = prepare_features(df)
    print(f"特征工程完成，共{df_features.shape[1]}个特征")
    
    # 划分训练集和测试集
    train_indices, test_indices = get_train_test_split(df_features)
    X_train = df_features.loc[train_indices]
    X_test = df_features.loc[test_indices]
    print(f"训练集大小: {len(X_train)}, 测试集大小: {len(X_test)}")
    
    # 获取测试集的日期列表
    test_dates = sorted(X_test['date'].unique())
    print(f"测试日期: {test_dates}")
    
    # 1. 运行Persistence基准模型
    start_time = time.time()
    persistence_pred = persistence_model(X_train, test_dates)
    persistence_time = time.time() - start_time
    print(f"Persistence模型完成，耗时: {persistence_time:.2f}秒")
    
    # 2. 运行ARIMA模型
    start_time = time.time()
    arima_pred = arima_model(X_train, test_dates)
    arima_time = time.time() - start_time
    print(f"ARIMA模型完成，耗时: {arima_time:.2f}秒")
    
    # 3. 运行Prophet模型
    start_time = time.time()
    prophet_pred = prophet_model(X_train, test_dates)
    prophet_time = time.time() - start_time
    print(f"Prophet模型完成，耗时: {prophet_time:.2f}秒")
    
    # 获取特征和标签
    y_train = X_train['power'].values
    y_test = X_test['power'].values
    
    # 4. 运行LightGBM模型
    start_time = time.time()
    lgbm_pred, lgbm_feat_imp = lgbm_model(X_train, y_train, X_test, test_dates)
    lgbm_time = time.time() - start_time
    print(f"LightGBM模型完成，耗时: {lgbm_time:.2f}秒")
    
    # 5. 运行XGBoost模型
    start_time = time.time()
    xgb_pred, xgb_feat_imp = xgboost_model(X_train, y_train, X_test)
    xgb_time = time.time() - start_time
    print(f"XGBoost模型完成，耗时: {xgb_time:.2f}秒")
    
    # 收集所有模型的结果
    predictions = {
        'Persistence': persistence_pred,
        'ARIMA': arima_pred,
        'Prophet': prophet_pred,
        'LightGBM': lgbm_pred,
        'XGBoost': xgb_pred
    }
    
    # 评估模型性能
    results = evaluate_models(y_test, predictions)
    print("\n所有模型评估结果:")
    print(results)
    
    # 保存评估结果
    results.to_csv(os.path.join(RESULTS_DIR, 'model_evaluation.csv'))
    
    # 绘制更新后的预测结果对比图
    plot_prediction_comparison(
        pd.Series(y_test), 
        predictions, 
        f"站点{station_id}不同模型预测效果对比", 
        os.path.join(RESULTS_DIR, 'prediction_comparison.png')
    )
    
    # 输出模型运行时间
    runtime = {
        'Persistence': persistence_time,
        'ARIMA': arima_time,
        'Prophet': prophet_time,
        'LightGBM': lgbm_time,
        'XGBoost': xgb_time
    }
    pd.DataFrame({'Runtime(s)': runtime}).to_csv(os.path.join(RESULTS_DIR, 'model_runtime.csv'))
    
    # 6. 运行TCN深度学习模型
    try:
        start_time = time.time()
        tcn_pred = tcn_model(X_train, y_train, X_test, test_dates)
        tcn_time = time.time() - start_time
        print(f"TCN模型完成，耗时: {tcn_time:.2f}秒")
        
        # 更新预测结果
        predictions['TCN'] = tcn_pred
        runtime['TCN'] = tcn_time
    except Exception as e:
        print(f"TCN模型运行出错: {e}")
    
    # 7. 执行模型集成
    ensemble_avg, ensemble_weighted = ensemble_model(predictions)
    predictions['Ensemble_Avg'] = ensemble_avg
    predictions['Ensemble_Weighted'] = ensemble_weighted
    
    # 重新评估所有模型性能
    results = evaluate_models(y_test, predictions)
    print("\n所有模型评估结果(含集成):")
    print(results)
    
    # 保存评估结果
    results.to_csv(os.path.join(RESULTS_DIR, 'model_evaluation_with_ensemble.csv'))
    
    # 更新运行时间表
    pd.DataFrame({'Runtime(s)': runtime}).to_csv(os.path.join(RESULTS_DIR, 'model_runtime.csv'))
    
    # 绘制最终预测结果对比图
    # 为了避免图表过于拥挤，只选择性能最好的几个模型和集成结果
    best_predictions = {
        'Persistence': predictions['Persistence'],  # 作为基准
        'Prophet': predictions['Prophet'],          # 统计模型代表
        'LightGBM': predictions['LightGBM'],        # 树模型代表
        'Ensemble_Weighted': predictions['Ensemble_Weighted']  # 最终集成结果
    }
    
    # 如果有TCN，也加入对比
    if 'TCN' in predictions:
        best_predictions['TCN'] = predictions['TCN']
    
    # 绘制最终对比图
    plot_prediction_comparison(
        pd.Series(y_test), 
        best_predictions, 
        f"站点{station_id}最优模型预测效果对比", 
        os.path.join(RESULTS_DIR, 'best_models_comparison.png')
    )
    
    # 将前两天（96*2点）单独绘制，以便更清晰地查看预测效果
    if len(y_test) > 96*2:
        sample_y = pd.Series(y_test[:96*2])
        sample_predictions = {model: pred[:96*2] for model, pred in best_predictions.items()}
        plot_prediction_comparison(
            sample_y, 
            sample_predictions, 
            f"站点{station_id}最优模型预测效果对比(前两天)", 
            os.path.join(RESULTS_DIR, 'best_models_comparison_2days.png')
        )
    
    print("\n全部模型与集成评估完成，结果已保存到", RESULTS_DIR)
    
    # 额外保存一个结果摘要，用于报告
    summary = pd.DataFrame({
        'Model': results.index,
        'MAE': results['MAE'],
        'nRMSE': results['nRMSE'],
        'SMAPE': results['SMAPE']
    })
    
    # 计算相对于Persistence基准的改进百分比
    base_mae = results.loc['Persistence', 'MAE']
    base_nrmse = results.loc['Persistence', 'nRMSE']
    base_smape = results.loc['Persistence', 'SMAPE']
    
    summary['MAE_Improvement(%)'] = (base_mae - summary['MAE']) / base_mae * 100
    summary['nRMSE_Improvement(%)'] = (base_nrmse - summary['nRMSE']) / base_nrmse * 100
    summary['SMAPE_Improvement(%)'] = (base_smape - summary['SMAPE']) / base_smape * 100
    
    summary.to_csv(os.path.join(RESULTS_DIR, 'model_summary.csv'), index=False)
    
    print("\n预测实验完成！") 