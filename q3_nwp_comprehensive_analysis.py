#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题3：融入NWP信息的光伏电站日前发电功率预测模型（详细分析版）

本程序专门针对问题3进行详细分析：
1. 使用第二问的所有12种模型
2. 对比四个站点的第二问和第三问结果
3. 提供详细的改善率指标和可视化分析
4. 生成全面的对比报告

作者：AI助手
日期：2024年
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, ExtraTreesRegressor
from sklearn.linear_model import Ridge, Lasso, ElasticNet
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
import lightgbm as lgb
import xgboost as xgb
from catboost import CatBoostRegressor
import torch
import torch.nn as nn
import torch.optim as optim
import warnings
import os
import time
from datetime import datetime
import json

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 创建结果目录
RESULTS_DIR = 'Q3_NWP_Analysis_Results'
os.makedirs(RESULTS_DIR, exist_ok=True)

# 设备配置
DEVICE = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"使用设备: {DEVICE}")

# 模型配置
MODEL_CONFIG = {
    'lgbm_rounds': 300,
    'xgb_estimators': 300,
    'catboost_iterations': 300,
    'rf_estimators': 100,
    'nn_epochs': 50,
    'random_state': 42
}

# 站点配置
STATIONS = [0, 1, 2, 3]  # 分析四个站点
STATION_NAMES = {0: '站点0', 1: '站点1', 2: '站点2', 3: '站点3'}

# 简单神经网络模型
class FastNN(nn.Module):
    def __init__(self, input_size):
        super(FastNN, self).__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_size, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1)
        )

    def forward(self, x):
        return self.layers(x)

def load_station_data(station_id):
    """加载站点数据"""
    file_path = f'dataset/station{station_id:02d}.csv'
    if not os.path.exists(file_path):
        print(f"警告：文件 {file_path} 不存在，跳过站点 {station_id}")
        return None

    df = pd.read_csv(file_path)
    df['date_time'] = pd.to_datetime(df['date_time'])
    df['date'] = df['date_time'].dt.date
    df['hour'] = df['date_time'].dt.hour
    df['minute'] = df['date_time'].dt.minute
    df['month'] = df['date_time'].dt.month
    df['day'] = df['date_time'].dt.day
    df['year'] = df['date_time'].dt.year
    df['dayofweek'] = df['date_time'].dt.dayofweek

    # 白昼时段标记（6:00-18:00）
    df['is_daytime'] = ((df['hour'] >= 6) & (df['hour'] < 18)).astype(int)

    return df

def prepare_features_q3(df, include_nwp=True):
    """准备第三问的特征（基准版本 vs NWP增强版本）"""
    df_features = df.copy()

    # 1. 基础时间特征
    df_features['hour_sin'] = np.sin(2 * np.pi * df_features['hour'] / 24)
    df_features['hour_cos'] = np.cos(2 * np.pi * df_features['hour'] / 24)
    df_features['month_sin'] = np.sin(2 * np.pi * df_features['month'] / 12)
    df_features['month_cos'] = np.cos(2 * np.pi * df_features['month'] / 12)
    df_features['dayofweek_sin'] = np.sin(2 * np.pi * df_features['dayofweek'] / 7)
    df_features['dayofweek_cos'] = np.cos(2 * np.pi * df_features['dayofweek'] / 7)

    # 2. 功率历史特征
    df_features['power_lag_1d'] = df_features['power'].shift(96)
    df_features['power_lag_2d'] = df_features['power'].shift(192)
    df_features['power_lag_7d'] = df_features['power'].shift(672)  # 一周前
    df_features['power_ma_6h'] = df_features['power'].rolling(window=24, min_periods=1).mean()
    df_features['power_ma_12h'] = df_features['power'].rolling(window=48, min_periods=1).mean()
    df_features['power_ma_24h'] = df_features['power'].rolling(window=96, min_periods=1).mean()
    df_features['power_std_6h'] = df_features['power'].rolling(window=24, min_periods=1).std()
    df_features['power_std_12h'] = df_features['power'].rolling(window=48, min_periods=1).std()

    # 3. NWP特征（仅在include_nwp=True时添加）
    if include_nwp:
        nwp_features = ['nwp_globalirrad', 'nwp_directirrad', 'nwp_temperature', 'nwp_humidity']

        for feature in nwp_features:
            if feature in df_features.columns:
                # 滞后特征（1天和2天）
                df_features[f'{feature}_lag_1d'] = df_features[feature].shift(96)
                df_features[f'{feature}_lag_2d'] = df_features[feature].shift(192)
                # 滑动平均（6小时和12小时）
                df_features[f'{feature}_ma_6h'] = df_features[feature].rolling(window=24, min_periods=1).mean()
                df_features[f'{feature}_ma_12h'] = df_features[feature].rolling(window=48, min_periods=1).mean()
                # 变化率
                df_features[f'{feature}_diff'] = df_features[feature].diff()
                # 滑动标准差
                df_features[f'{feature}_std_6h'] = df_features[feature].rolling(window=24, min_periods=1).std()

        # 理论效率特征
        if 'nwp_globalirrad' in df_features.columns:
            # 使用简化的理论模型：理论功率 = 辐照度 * 效率系数
            df_features['theoretical_power'] = df_features['nwp_globalirrad'] * 0.85 / 1000
            # 相对效率
            df_features['relative_efficiency'] = np.where(
                df_features['theoretical_power'] > 0,
                df_features['power'] / df_features['theoretical_power'],
                np.nan
            )
            df_features['relative_efficiency_lag_1d'] = df_features['relative_efficiency'].shift(96)

    # 4. 处理缺失值
    for col in df_features.columns:
        if df_features[col].dtype in [np.float64, np.float32, np.int64, np.int32]:
            df_features[col] = df_features[col].fillna(df_features[col].median())

    return df_features

def get_train_test_split_q3(df):
    """按照问题3的要求划分训练集和测试集"""
    test_months = [2, 5, 8, 11]
    test_indices = []

    for month in test_months:
        month_data = df[df['month'] == month]
        if len(month_data) > 0:
            # 获取该月的最后一周数据（最后7天）
            month_dates = sorted(month_data['date'].unique())
            if len(month_dates) >= 7:
                last_week_dates = month_dates[-7:]  # 最后7天
                last_week_indices = month_data[month_data['date'].isin(last_week_dates)].index
                test_indices.extend(last_week_indices)

    # 训练集是除了测试集之外的所有数据
    train_indices = df.index.difference(test_indices)

    return train_indices, test_indices

def persistence_model(X_train, test_dates):
    """基准模型：使用前一天同时刻的功率作为预测"""
    predictions = []

    # 获取训练数据的最后一天作为预测基准
    last_day_power = X_train['power_lag_1d'].iloc[-96:].values  # 最后一天的96个时间点

    # 对每个测试日期重复这个模式
    for _ in test_dates:
        predictions.extend(last_day_power)

    return np.array(predictions[:len(test_dates) * 96])

def build_all_models_q3(X_train, y_train, X_test, model_type='baseline'):
    """
    构建第二问的所有12种模型（问题3专用版本）

    Args:
        X_train: 训练特征
        y_train: 训练标签
        X_test: 测试特征
        model_type: 模型类型 ('baseline', 'nwp')
    """
    models = {}
    predictions = {}

    print(f"=== 构建{model_type}模型组 ===")

    # 根据模型类型选择特征
    if model_type == 'baseline':
        # 基准模型：不使用NWP特征
        feature_cols = [col for col in X_train.columns
                       if not col.startswith('nwp_') and not 'theoretical' in col and not 'relative_efficiency' in col]
        print(f"基准模型特征数量: {len(feature_cols)}")
    else:  # nwp
        # NWP模型：使用所有特征
        feature_cols = list(X_train.columns)
        print(f"NWP模型特征数量: {len(feature_cols)}")

    X_train_model = X_train[feature_cols]
    X_test_model = X_test[feature_cols]

    # 1. LightGBM
    print("训练LightGBM模型...")
    lgbm_model = lgb.LGBMRegressor(
        n_estimators=MODEL_CONFIG['lgbm_rounds'],
        learning_rate=0.05,
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbose=-1
    )
    lgbm_model.fit(X_train_model, y_train)
    models[f'LightGBM_{model_type}'] = lgbm_model
    predictions[f'LightGBM_{model_type}'] = lgbm_model.predict(X_test_model)

    # 2. XGBoost
    print("训练XGBoost模型...")
    xgb_model = xgb.XGBRegressor(
        n_estimators=MODEL_CONFIG['xgb_estimators'],
        learning_rate=0.05,
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbosity=0
    )
    xgb_model.fit(X_train_model, y_train)
    models[f'XGBoost_{model_type}'] = xgb_model
    predictions[f'XGBoost_{model_type}'] = xgb_model.predict(X_test_model)

    # 3. CatBoost
    print("训练CatBoost模型...")
    catboost_model = CatBoostRegressor(
        iterations=MODEL_CONFIG['catboost_iterations'],
        learning_rate=0.05,
        depth=10,
        random_state=MODEL_CONFIG['random_state'],
        verbose=False
    )
    catboost_model.fit(X_train_model, y_train)
    models[f'CatBoost_{model_type}'] = catboost_model
    predictions[f'CatBoost_{model_type}'] = catboost_model.predict(X_test_model)

    # 4. Random Forest
    print("训练RandomForest模型...")
    rf_model = RandomForestRegressor(
        n_estimators=MODEL_CONFIG['rf_estimators'],
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        n_jobs=-1
    )
    rf_model.fit(X_train_model, y_train)
    models[f'RandomForest_{model_type}'] = rf_model
    predictions[f'RandomForest_{model_type}'] = rf_model.predict(X_test_model)

    # 5. Extra Trees
    print("训练ExtraTrees模型...")
    et_model = ExtraTreesRegressor(
        n_estimators=MODEL_CONFIG['rf_estimators'],
        max_depth=10,
        random_state=MODEL_CONFIG['random_state'],
        n_jobs=-1
    )
    et_model.fit(X_train_model, y_train)
    models[f'ExtraTrees_{model_type}'] = et_model
    predictions[f'ExtraTrees_{model_type}'] = et_model.predict(X_test_model)

    # 6. Gradient Boosting
    print("训练GradientBoosting模型...")
    gb_model = GradientBoostingRegressor(
        n_estimators=100,
        learning_rate=0.1,
        max_depth=6,
        random_state=MODEL_CONFIG['random_state']
    )
    gb_model.fit(X_train_model, y_train)
    models[f'GradientBoosting_{model_type}'] = gb_model
    predictions[f'GradientBoosting_{model_type}'] = gb_model.predict(X_test_model)

    # 7-9. 线性模型（需要标准化）
    print("训练线性模型...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train_model)
    X_test_scaled = scaler.transform(X_test_model)

    # Ridge回归
    ridge_model = Ridge(alpha=1.0, random_state=MODEL_CONFIG['random_state'])
    ridge_model.fit(X_train_scaled, y_train)
    models[f'Ridge_{model_type}'] = ridge_model
    predictions[f'Ridge_{model_type}'] = ridge_model.predict(X_test_scaled)

    # Lasso回归
    lasso_model = Lasso(alpha=0.1, random_state=MODEL_CONFIG['random_state'])
    lasso_model.fit(X_train_scaled, y_train)
    models[f'Lasso_{model_type}'] = lasso_model
    predictions[f'Lasso_{model_type}'] = lasso_model.predict(X_test_scaled)

    # ElasticNet回归
    elastic_model = ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=MODEL_CONFIG['random_state'])
    elastic_model.fit(X_train_scaled, y_train)
    models[f'ElasticNet_{model_type}'] = elastic_model
    predictions[f'ElasticNet_{model_type}'] = elastic_model.predict(X_test_scaled)

    # 10. 神经网络模型
    print("训练神经网络模型...")
    try:
        # 转换为张量
        X_train_tensor = torch.FloatTensor(X_train_scaled).to(DEVICE)
        y_train_tensor = torch.FloatTensor(y_train.values.reshape(-1, 1)).to(DEVICE)
        X_test_tensor = torch.FloatTensor(X_test_scaled).to(DEVICE)

        # 创建模型
        nn_model = FastNN(X_train_scaled.shape[1]).to(DEVICE)
        criterion = nn.MSELoss()
        optimizer = optim.Adam(nn_model.parameters(), lr=0.001)

        # 训练模型
        nn_model.train()
        for epoch in range(MODEL_CONFIG['nn_epochs']):
            optimizer.zero_grad()
            outputs = nn_model(X_train_tensor)
            loss = criterion(outputs, y_train_tensor)
            loss.backward()
            optimizer.step()

        # 预测
        nn_model.eval()
        with torch.no_grad():
            nn_pred = nn_model(X_test_tensor).cpu().numpy().flatten()

        models[f'FastNN_{model_type}'] = nn_model
        predictions[f'FastNN_{model_type}'] = nn_pred

    except Exception as e:
        print(f"神经网络模型训练失败: {e}")

    # 11. 模型集成（简单平均）
    print("构建集成模型...")
    if len(predictions) > 0:
        # 计算所有模型预测的平均值
        pred_values = list(predictions.values())
        ensemble_pred = np.mean(pred_values, axis=0)
        predictions[f'Ensemble_{model_type}'] = ensemble_pred

    return models, predictions

def evaluate_models_q3(y_true, predictions, is_daytime):
    """评估模型性能（问题3专用版本）"""
    results = []

    # 只在白昼时段评估
    daytime_mask = is_daytime.astype(bool)

    for model_name, y_pred in predictions.items():
        # 白昼时段评估
        y_true_day = y_true[daytime_mask]
        y_pred_day = y_pred[daytime_mask]

        if len(y_true_day) == 0:
            continue

        # 计算评估指标
        mae = mean_absolute_error(y_true_day, y_pred_day)
        rmse = np.sqrt(mean_squared_error(y_true_day, y_pred_day))
        r2 = r2_score(y_true_day, y_pred_day)

        # 计算MAPE（平均绝对百分比误差）
        mape = np.mean(np.abs((y_true_day - y_pred_day) / (y_true_day + 1e-8))) * 100

        # 计算NMAE（归一化平均绝对误差）
        nmae = mae / (np.max(y_true_day) - np.min(y_true_day)) * 100

        results.append({
            'Model': model_name,
            'MAE': mae,
            'RMSE': rmse,
            'R²': r2,
            'MAPE': mape,
            'NMAE': nmae
        })

    return pd.DataFrame(results)

def calculate_improvement_metrics(baseline_results, nwp_results):
    """计算改善率指标"""
    improvement_metrics = []

    # 提取模型类型（去掉_baseline和_nwp后缀）
    baseline_models = {}
    nwp_models = {}

    for _, row in baseline_results.iterrows():
        model_name = row['Model'].replace('_baseline', '')
        baseline_models[model_name] = row

    for _, row in nwp_results.iterrows():
        model_name = row['Model'].replace('_nwp', '')
        nwp_models[model_name] = row

    # 计算每个模型的改善率
    for model_name in baseline_models.keys():
        if model_name in nwp_models:
            baseline = baseline_models[model_name]
            nwp = nwp_models[model_name]

            # 计算各种改善率
            mae_improvement = (baseline['MAE'] - nwp['MAE']) / baseline['MAE'] * 100
            rmse_improvement = (baseline['RMSE'] - nwp['RMSE']) / baseline['RMSE'] * 100
            r2_improvement = (nwp['R²'] - baseline['R²']) / abs(baseline['R²']) * 100
            mape_improvement = (baseline['MAPE'] - nwp['MAPE']) / baseline['MAPE'] * 100
            nmae_improvement = (baseline['NMAE'] - nwp['NMAE']) / baseline['NMAE'] * 100

            improvement_metrics.append({
                'Model': model_name,
                'Baseline_MAE': baseline['MAE'],
                'NWP_MAE': nwp['MAE'],
                'MAE_Improvement_%': mae_improvement,
                'Baseline_RMSE': baseline['RMSE'],
                'NWP_RMSE': nwp['RMSE'],
                'RMSE_Improvement_%': rmse_improvement,
                'Baseline_R²': baseline['R²'],
                'NWP_R²': nwp['R²'],
                'R²_Improvement_%': r2_improvement,
                'Baseline_MAPE': baseline['MAPE'],
                'NWP_MAPE': nwp['MAPE'],
                'MAPE_Improvement_%': mape_improvement,
                'Baseline_NMAE': baseline['NMAE'],
                'NWP_NMAE': nwp['NMAE'],
                'NMAE_Improvement_%': nmae_improvement
            })

    return pd.DataFrame(improvement_metrics)

def plot_q3_comprehensive_analysis(all_station_results, all_improvement_metrics, save_dir):
    """绘制问题3的全面分析图表"""

    # 1. 四站点模型性能对比热力图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('问题3：融入NWP信息的效果分析 - 四站点全面对比', fontsize=16, fontweight='bold')

    # 为每个站点创建热力图
    for i, station_id in enumerate(STATIONS):
        if station_id not in all_station_results:
            continue

        ax = axes[i//2, i%2]

        # 获取该站点的改善率数据
        improvement_df = all_improvement_metrics[station_id]

        # 创建热力图数据
        metrics = ['MAE_Improvement_%', 'RMSE_Improvement_%', 'R²_Improvement_%', 'MAPE_Improvement_%']
        heatmap_data = improvement_df[['Model'] + metrics].set_index('Model')

        # 绘制热力图
        sns.heatmap(heatmap_data, annot=True, fmt='.2f', cmap='RdYlGn', center=0,
                   ax=ax, cbar_kws={'label': '改善率 (%)'})
        ax.set_title(f'{STATION_NAMES[station_id]} - NWP信息改善率', fontsize=14)
        ax.set_xlabel('评估指标')
        ax.set_ylabel('模型类型')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'q3_station_improvement_heatmap.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 各站点最佳模型对比
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('问题3：各站点最佳模型性能对比', fontsize=16, fontweight='bold')

    metrics_to_plot = ['MAE', 'RMSE', 'R²', 'MAPE']

    for i, metric in enumerate(metrics_to_plot):
        ax = axes[i//2, i%2]

        station_data = []
        baseline_data = []
        nwp_data = []

        for station_id in STATIONS:
            if station_id not in all_station_results:
                continue

            baseline_results, nwp_results = all_station_results[station_id]

            # 找到最佳模型（基于R²）
            best_baseline = baseline_results.loc[baseline_results['R²'].idxmax()]
            best_nwp = nwp_results.loc[nwp_results['R²'].idxmax()]

            station_data.append(STATION_NAMES[station_id])
            baseline_data.append(best_baseline[metric])
            nwp_data.append(best_nwp[metric])

        x = np.arange(len(station_data))
        width = 0.35

        bars1 = ax.bar(x - width/2, baseline_data, width, label='基准模型（第二问）', alpha=0.8, color='red')
        bars2 = ax.bar(x + width/2, nwp_data, width, label='NWP增强模型（第三问）', alpha=0.8, color='blue')

        ax.set_xlabel('站点')
        ax.set_ylabel(metric)
        ax.set_title(f'各站点最佳模型{metric}对比')
        ax.set_xticks(x)
        ax.set_xticklabels(station_data)
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.4f}', ha='center', va='bottom', fontsize=10)

        for bar in bars2:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{height:.4f}', ha='center', va='bottom', fontsize=10)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'q3_best_models_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 3. 模型类型改善率统计
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('问题3：各模型类型在不同站点的改善率统计', fontsize=16, fontweight='bold')

    # 合并所有站点的改善率数据
    all_improvements = []
    for station_id in STATIONS:
        if station_id in all_improvement_metrics:
            df = all_improvement_metrics[station_id].copy()
            df['Station'] = STATION_NAMES[station_id]
            all_improvements.append(df)

    if all_improvements:
        combined_improvements = pd.concat(all_improvements, ignore_index=True)

        metrics_to_analyze = ['MAE_Improvement_%', 'RMSE_Improvement_%', 'R²_Improvement_%', 'MAPE_Improvement_%']

        for i, metric in enumerate(metrics_to_analyze):
            ax = axes[i//2, i%2]

            # 创建箱线图
            combined_improvements.boxplot(column=metric, by='Model', ax=ax)
            ax.set_title(f'{metric.replace("_", " ")} 分布')
            ax.set_xlabel('模型类型')
            ax.set_ylabel('改善率 (%)')
            ax.tick_params(axis='x', rotation=45)
            ax.grid(True, alpha=0.3)
            ax.axhline(y=0, color='red', linestyle='--', alpha=0.7)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'q3_improvement_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()

def plot_comprehensive_prediction_comparison(all_prediction_data, save_dir):
    """绘制四站点综合预测对比图"""

    fig, axes = plt.subplots(2, 2, figsize=(24, 16))
    fig.suptitle('问题3：四站点第二问vs第三问预测效果综合对比', fontsize=18, fontweight='bold')

    for i, station_id in enumerate(STATIONS):
        if station_id not in all_prediction_data:
            continue

        ax = axes[i//2, i%2]

        test_times, y_true, baseline_predictions, nwp_predictions, is_daytime = all_prediction_data[station_id]

        # 只显示白昼时段的数据
        daytime_mask = is_daytime.astype(bool)
        test_times_day = test_times[daytime_mask]
        y_true_day = y_true[daytime_mask]

        # 找到最佳模型
        baseline_mae = {}
        nwp_mae = {}

        for model_name, predictions in baseline_predictions.items():
            pred_day = predictions[daytime_mask]
            baseline_mae[model_name] = mean_absolute_error(y_true_day, pred_day)

        for model_name, predictions in nwp_predictions.items():
            pred_day = predictions[daytime_mask]
            nwp_mae[model_name] = mean_absolute_error(y_true_day, pred_day)

        best_baseline_model = min(baseline_mae, key=baseline_mae.get)
        best_nwp_model = min(nwp_mae, key=nwp_mae.get)

        best_baseline_pred = baseline_predictions[best_baseline_model][daytime_mask]
        best_nwp_pred = nwp_predictions[best_nwp_model][daytime_mask]

        # 只显示前3天的数据以保持清晰度
        n_points = min(len(test_times_day), 3*96)  # 3天 * 96个15分钟点

        ax.plot(test_times_day[:n_points], y_true_day[:n_points],
                label='实际功率', color='black', linewidth=2, alpha=0.8)
        ax.plot(test_times_day[:n_points], best_baseline_pred[:n_points],
                label=f'基准最佳({best_baseline_model.replace("_baseline", "")})',
                color='red', linewidth=1.5, alpha=0.7)
        ax.plot(test_times_day[:n_points], best_nwp_pred[:n_points],
                label=f'NWP最佳({best_nwp_model.replace("_nwp", "")})',
                color='blue', linewidth=1.5, alpha=0.7)

        # 计算改善率
        improvement = (baseline_mae[best_baseline_model] - nwp_mae[best_nwp_model]) / baseline_mae[best_baseline_model] * 100

        ax.set_title(f'{STATION_NAMES[station_id]} (MAE改善: {improvement:.1f}%)')
        ax.set_xlabel('时间')
        ax.set_ylabel('功率 (MW)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='x', rotation=45)

        # 添加性能指标文本
        ax.text(0.02, 0.98, f'基准MAE: {baseline_mae[best_baseline_model]:.4f}\nNWP MAE: {nwp_mae[best_nwp_model]:.4f}',
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'q3_four_stations_prediction_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print("四站点综合预测对比图已保存")

def plot_four_stations_time_series(all_prediction_data, save_dir):
    """绘制四站点时间序列综合对比图（类似问题二格式）"""

    fig, axes = plt.subplots(2, 2, figsize=(28, 20))
    fig.suptitle('问题3：四站点时间序列预测对比（基于功率判定白天黑夜）', fontsize=20, fontweight='bold')

    for i, station_id in enumerate(STATIONS):
        if station_id not in all_prediction_data:
            continue

        ax = axes[i//2, i%2]

        test_times, y_true, baseline_predictions, nwp_predictions, _ = all_prediction_data[station_id]

        # 基于功率判定白天黑夜
        is_daytime_power = determine_daytime_by_power(y_true)

        # 找到最佳模型
        baseline_mae = {}
        nwp_mae = {}

        daytime_mask = is_daytime_power.astype(bool)
        y_true_day = y_true[daytime_mask]

        for model_name, predictions in baseline_predictions.items():
            if len(predictions[daytime_mask]) > 0:
                baseline_mae[model_name] = mean_absolute_error(y_true_day, predictions[daytime_mask])

        for model_name, predictions in nwp_predictions.items():
            if len(predictions[daytime_mask]) > 0:
                nwp_mae[model_name] = mean_absolute_error(y_true_day, predictions[daytime_mask])

        if not baseline_mae or not nwp_mae:
            continue

        best_baseline_model = min(baseline_mae, key=baseline_mae.get)
        best_nwp_model = min(nwp_mae, key=nwp_mae.get)

        # 绘制时间序列（显示前10天数据）
        time_hours = np.arange(len(test_times))
        max_points = min(len(test_times), 10*96)  # 10天的数据点

        # 实际功率
        ax.plot(time_hours[:max_points], y_true[:max_points],
                label='实际功率', color='black', linewidth=2, alpha=0.8)

        # 最佳基准模型
        ax.plot(time_hours[:max_points], baseline_predictions[best_baseline_model][:max_points],
                label=f'基准最佳({best_baseline_model.replace("_baseline", "")})',
                color='red', linewidth=1.5, alpha=0.7)

        # 最佳NWP模型
        ax.plot(time_hours[:max_points], nwp_predictions[best_nwp_model][:max_points],
                label=f'NWP最佳({best_nwp_model.replace("_nwp", "")})',
                color='blue', linewidth=1.5, alpha=0.7)

        # 标记夜晚区域
        for j in range(min(len(test_times), max_points)):
            if is_daytime_power[j] == 0:  # 夜晚
                ax.axvspan(j-0.5, j+0.5, alpha=0.05, color='gray')

        # 计算改善率
        improvement = (baseline_mae[best_baseline_model] - nwp_mae[best_nwp_model]) / baseline_mae[best_baseline_model] * 100

        ax.set_title(f'{STATION_NAMES[station_id]}\n'
                    f'基准MAE: {baseline_mae[best_baseline_model]:.4f} → NWP MAE: {nwp_mae[best_nwp_model]:.4f}\n'
                    f'改善率: {improvement:.1f}% | 白昼点: {np.sum(is_daytime_power)}/{len(is_daytime_power)}',
                    fontsize=14)
        ax.set_xlabel('时间点（15分钟间隔）')
        ax.set_ylabel('功率 (MW)')
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'q3_four_stations_time_series_comparison.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print("四站点时间序列综合对比图已保存")

def determine_daytime_by_power(y_true, power_threshold=0.01):
    """基于功率判定白天黑夜（适应不同时区）"""
    # 使用功率阈值判定白天：功率大于阈值认为是白天
    is_daytime_power = (y_true > power_threshold).astype(int)
    return is_daytime_power

def plot_time_series_comparison(test_times, y_true, baseline_predictions, nwp_predictions,
                               station_id, save_dir):
    """绘制时间序列对比图（类似问题二的格式）"""

    # 基于功率判定白天黑夜
    is_daytime_power = determine_daytime_by_power(y_true)

    # 找到最佳模型
    baseline_mae = {}
    nwp_mae = {}

    # 只在白昼时段评估
    daytime_mask = is_daytime_power.astype(bool)
    y_true_day = y_true[daytime_mask]

    for model_name, predictions in baseline_predictions.items():
        if len(predictions[daytime_mask]) > 0:
            baseline_mae[model_name] = mean_absolute_error(y_true_day, predictions[daytime_mask])

    for model_name, predictions in nwp_predictions.items():
        if len(predictions[daytime_mask]) > 0:
            nwp_mae[model_name] = mean_absolute_error(y_true_day, predictions[daytime_mask])

    if not baseline_mae or not nwp_mae:
        print(f"站点{station_id}没有足够的白昼数据进行对比")
        return

    best_baseline_model = min(baseline_mae, key=baseline_mae.get)
    best_nwp_model = min(nwp_mae, key=nwp_mae.get)

    # 创建大图：类似问题二的格式
    fig = plt.figure(figsize=(24, 20))

    # 主标题
    fig.suptitle(f'{STATION_NAMES[station_id]} - 第二问vs第三问时间序列预测对比\n'
                f'基于功率判定白天黑夜（阈值=0.01MW）', fontsize=20, fontweight='bold')

    # 1. 完整时间序列图（上半部分，占60%）
    ax1 = plt.subplot2grid((5, 2), (0, 0), colspan=2, rowspan=3)

    # 绘制完整时间序列
    time_hours = np.arange(len(test_times))

    # 实际功率
    ax1.plot(time_hours, y_true, label='实际功率', color='black', linewidth=2, alpha=0.8)

    # 最佳基准模型
    ax1.plot(time_hours, baseline_predictions[best_baseline_model],
             label=f'基准最佳 ({best_baseline_model.replace("_baseline", "")})',
             color='red', linewidth=1.5, alpha=0.7)

    # 最佳NWP模型
    ax1.plot(time_hours, nwp_predictions[best_nwp_model],
             label=f'NWP最佳 ({best_nwp_model.replace("_nwp", "")})',
             color='blue', linewidth=1.5, alpha=0.7)

    # 标记白天黑夜区域
    for i in range(len(test_times)):
        if is_daytime_power[i] == 0:  # 夜晚
            ax1.axvspan(i-0.5, i+0.5, alpha=0.1, color='gray')

    ax1.set_title('完整时间序列预测对比', fontsize=16)
    ax1.set_xlabel('时间点（15分钟间隔）')
    ax1.set_ylabel('功率 (MW)')
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)

    # 添加性能指标文本
    improvement = (baseline_mae[best_baseline_model] - nwp_mae[best_nwp_model]) / baseline_mae[best_baseline_model] * 100
    ax1.text(0.02, 0.98,
             f'基准MAE: {baseline_mae[best_baseline_model]:.4f}\n'
             f'NWP MAE: {nwp_mae[best_nwp_model]:.4f}\n'
             f'改善率: {improvement:.1f}%\n'
             f'白昼数据点: {np.sum(is_daytime_power)}/{len(is_daytime_power)}',
             transform=ax1.transAxes, verticalalignment='top', fontsize=12,
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 2. 白昼时段放大图（左下）
    ax2 = plt.subplot2grid((5, 2), (3, 0), rowspan=2)

    # 只显示白昼时段
    daytime_indices = np.where(is_daytime_power)[0]
    if len(daytime_indices) > 0:
        # 选择前7天的白昼数据进行展示
        max_points = min(len(daytime_indices), 7*24*4)  # 7天的数据点
        show_indices = daytime_indices[:max_points]

        ax2.plot(show_indices, y_true[show_indices],
                label='实际功率', color='black', linewidth=2, alpha=0.8)
        ax2.plot(show_indices, baseline_predictions[best_baseline_model][show_indices],
                label=f'基准最佳', color='red', linewidth=1.5, alpha=0.7)
        ax2.plot(show_indices, nwp_predictions[best_nwp_model][show_indices],
                label=f'NWP最佳', color='blue', linewidth=1.5, alpha=0.7)

        ax2.set_title('白昼时段详细对比（前7天）', fontsize=14)
        ax2.set_xlabel('时间点')
        ax2.set_ylabel('功率 (MW)')
        ax2.legend(fontsize=10)
        ax2.grid(True, alpha=0.3)

    # 3. 误差对比图（右下）
    ax3 = plt.subplot2grid((5, 2), (3, 1), rowspan=2)

    if len(y_true_day) > 0:
        baseline_errors = baseline_predictions[best_baseline_model][daytime_mask] - y_true_day
        nwp_errors = nwp_predictions[best_nwp_model][daytime_mask] - y_true_day

        ax3.hist(baseline_errors, bins=30, alpha=0.7, color='red',
                label=f'基准误差 (std={np.std(baseline_errors):.4f})', density=True)
        ax3.hist(nwp_errors, bins=30, alpha=0.7, color='blue',
                label=f'NWP误差 (std={np.std(nwp_errors):.4f})', density=True)

        ax3.axvline(0, color='black', linestyle='--', alpha=0.8)
        ax3.set_title('白昼时段误差分布对比', fontsize=14)
        ax3.set_xlabel('预测误差 (MW)')
        ax3.set_ylabel('密度')
        ax3.legend(fontsize=10)
        ax3.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图表
    save_path = os.path.join(save_dir, f'station_{station_id}_time_series_comparison.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"站点{station_id}时间序列对比图已保存: {save_path}")

    return save_path

def create_comprehensive_prediction_table(all_prediction_data, save_dir):
    """创建四站点综合预测结果表格"""

    comprehensive_results = []

    for station_id in STATIONS:
        if station_id not in all_prediction_data:
            continue

        test_times, y_true, baseline_predictions, nwp_predictions, is_daytime = all_prediction_data[station_id]

        # 基于功率判定白天黑夜
        is_daytime_power = determine_daytime_by_power(y_true)
        daytime_mask = is_daytime_power.astype(bool)
        test_times_day = test_times[daytime_mask]
        y_true_day = y_true[daytime_mask]

        # 为每个时间点创建记录
        for i, (time_point, actual_power) in enumerate(zip(test_times_day, y_true_day)):
            record = {
                'Station': STATION_NAMES[station_id],
                'DateTime': time_point,
                'Actual_Power_MW': actual_power
            }

            # 添加基准模型预测
            for model_name, predictions in baseline_predictions.items():
                pred_value = predictions[daytime_mask][i]
                record[f'{model_name}_Prediction_MW'] = pred_value
                record[f'{model_name}_Error_MW'] = pred_value - actual_power

            # 添加NWP模型预测
            for model_name, predictions in nwp_predictions.items():
                pred_value = predictions[daytime_mask][i]
                record[f'{model_name}_Prediction_MW'] = pred_value
                record[f'{model_name}_Error_MW'] = pred_value - actual_power

            comprehensive_results.append(record)

    # 转换为DataFrame并保存
    comprehensive_df = pd.DataFrame(comprehensive_results)
    comprehensive_path = os.path.join(save_dir, 'q3_comprehensive_prediction_results.csv')
    comprehensive_df.to_csv(comprehensive_path, index=False)

    print(f"四站点综合预测结果表格已保存: {comprehensive_path}")

    return comprehensive_df

def save_prediction_results_table(test_times, y_true, baseline_predictions, nwp_predictions,
                                 is_daytime, station_id, save_dir):
    """保存类似Q2_fast_results\prediction_results_table2.csv格式的预测结果表格"""

    # 基于功率判定白天黑夜
    is_daytime_power = determine_daytime_by_power(y_true)

    # 创建基础表格结构
    results_table = pd.DataFrame({
        '起报时间': test_times,
        '预报时间': test_times,
        '实际功率(MW)': y_true,
        '是否白昼(时间)': is_daytime,
        '是否白昼(功率)': is_daytime_power
    })

    # 添加基准模型预测结果
    for model_name, predictions in baseline_predictions.items():
        results_table[f'{model_name}_预测功率(MW)'] = predictions
        results_table[f'{model_name}_误差(MW)'] = predictions - y_true
        results_table[f'{model_name}_相对误差(%)'] = (predictions - y_true) / (y_true + 1e-8) * 100

    # 添加NWP增强模型预测结果
    for model_name, predictions in nwp_predictions.items():
        results_table[f'{model_name}_预测功率(MW)'] = predictions
        results_table[f'{model_name}_误差(MW)'] = predictions - y_true
        results_table[f'{model_name}_相对误差(%)'] = (predictions - y_true) / (y_true + 1e-8) * 100

    # 保存完整结果
    full_path = os.path.join(save_dir, f'station_{station_id}_prediction_results_table_full.csv')
    results_table.to_csv(full_path, index=False)

    # 保存白昼时段结果（基于功率判定）
    daytime_results = results_table[results_table['是否白昼(功率)'] == 1].copy()
    daytime_path = os.path.join(save_dir, f'station_{station_id}_prediction_results_table.csv')
    daytime_results.to_csv(daytime_path, index=False)

    print(f"站点{station_id}预测结果表格已保存: {daytime_path}")
    print(f"  基于功率判定的白昼数据点: {np.sum(is_daytime_power)}/{len(is_daytime_power)}")

    return results_table, daytime_results

def plot_prediction_comparison_curves(test_times, y_true, baseline_predictions, nwp_predictions,
                                    is_daytime, station_id, save_dir):
    """绘制预测对比曲线图"""

    # 只显示白昼时段的数据
    daytime_mask = is_daytime.astype(bool)
    test_times_day = test_times[daytime_mask]
    y_true_day = y_true[daytime_mask]

    # 选择最佳的基准模型和NWP模型进行对比
    baseline_mae = {}
    nwp_mae = {}

    for model_name, predictions in baseline_predictions.items():
        pred_day = predictions[daytime_mask]
        baseline_mae[model_name] = mean_absolute_error(y_true_day, pred_day)

    for model_name, predictions in nwp_predictions.items():
        pred_day = predictions[daytime_mask]
        nwp_mae[model_name] = mean_absolute_error(y_true_day, pred_day)

    # 找到最佳模型
    best_baseline_model = min(baseline_mae, key=baseline_mae.get)
    best_nwp_model = min(nwp_mae, key=nwp_mae.get)

    best_baseline_pred = baseline_predictions[best_baseline_model][daytime_mask]
    best_nwp_pred = nwp_predictions[best_nwp_model][daytime_mask]

    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle(f'{STATION_NAMES[station_id]} - 第二问vs第三问预测效果对比', fontsize=16, fontweight='bold')

    # 1. 时间序列对比图
    ax1 = axes[0, 0]

    # 只显示前7天的数据以保持清晰度
    n_points = min(len(test_times_day), 7*96)  # 7天 * 96个15分钟点

    ax1.plot(test_times_day[:n_points], y_true_day[:n_points],
             label='实际功率', color='black', linewidth=2, alpha=0.8)
    ax1.plot(test_times_day[:n_points], best_baseline_pred[:n_points],
             label=f'基准最佳({best_baseline_model})', color='red', linewidth=1.5, alpha=0.7)
    ax1.plot(test_times_day[:n_points], best_nwp_pred[:n_points],
             label=f'NWP最佳({best_nwp_model})', color='blue', linewidth=1.5, alpha=0.7)

    ax1.set_title('时间序列预测对比（前7天）')
    ax1.set_xlabel('时间')
    ax1.set_ylabel('功率 (MW)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)

    # 2. 散点图对比
    ax2 = axes[0, 1]

    # 基准模型散点图
    ax2.scatter(y_true_day, best_baseline_pred, alpha=0.6, color='red', s=20,
               label=f'基准模型 (MAE={baseline_mae[best_baseline_model]:.4f})')
    # NWP模型散点图
    ax2.scatter(y_true_day, best_nwp_pred, alpha=0.6, color='blue', s=20,
               label=f'NWP模型 (MAE={nwp_mae[best_nwp_model]:.4f})')

    # 理想预测线
    max_power = max(y_true_day.max(), best_baseline_pred.max(), best_nwp_pred.max())
    ax2.plot([0, max_power], [0, max_power], 'k--', alpha=0.8, label='理想预测')

    ax2.set_title('预测值vs实际值散点图')
    ax2.set_xlabel('实际功率 (MW)')
    ax2.set_ylabel('预测功率 (MW)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 误差分布对比
    ax3 = axes[1, 0]

    baseline_errors = best_baseline_pred - y_true_day
    nwp_errors = best_nwp_pred - y_true_day

    ax3.hist(baseline_errors, bins=50, alpha=0.7, color='red',
             label=f'基准模型误差 (std={np.std(baseline_errors):.4f})', density=True)
    ax3.hist(nwp_errors, bins=50, alpha=0.7, color='blue',
             label=f'NWP模型误差 (std={np.std(nwp_errors):.4f})', density=True)

    ax3.axvline(0, color='black', linestyle='--', alpha=0.8)
    ax3.set_title('预测误差分布对比')
    ax3.set_xlabel('预测误差 (MW)')
    ax3.set_ylabel('密度')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 多模型MAE对比柱状图
    ax4 = axes[1, 1]

    # 选择前5个最佳模型进行对比
    baseline_sorted = sorted(baseline_mae.items(), key=lambda x: x[1])[:5]
    nwp_sorted = sorted(nwp_mae.items(), key=lambda x: x[1])[:5]

    models = [item[0] for item in baseline_sorted]
    baseline_maes = [item[1] for item in baseline_sorted]
    nwp_maes = [nwp_mae.get(model, float('inf')) for model in models]

    x = np.arange(len(models))
    width = 0.35

    bars1 = ax4.bar(x - width/2, baseline_maes, width, label='基准模型', alpha=0.8, color='red')
    bars2 = ax4.bar(x + width/2, nwp_maes, width, label='NWP模型', alpha=0.8, color='blue')

    ax4.set_title('前5名模型MAE对比')
    ax4.set_xlabel('模型类型')
    ax4.set_ylabel('MAE (MW)')
    ax4.set_xticks(x)
    ax4.set_xticklabels([m.replace('_baseline', '').replace('_nwp', '') for m in models], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{height:.4f}', ha='center', va='bottom', fontsize=9)

    for bar in bars2:
        height = bar.get_height()
        if height != float('inf'):
            ax4.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.4f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()

    # 保存图表
    save_path = os.path.join(save_dir, f'station_{station_id}_prediction_comparison_curves.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"站点{station_id}预测对比曲线已保存: {save_path}")

    return save_path

def save_q3_results(all_station_results, all_improvement_metrics, all_prediction_data, save_dir):
    """保存问题3的所有结果"""

    # 1. 保存各站点的详细结果
    for station_id in STATIONS:
        if station_id not in all_station_results:
            continue

        baseline_results, nwp_results = all_station_results[station_id]
        improvement_metrics = all_improvement_metrics[station_id]

        # 保存基准结果
        baseline_results.to_csv(
            os.path.join(save_dir, f'station_{station_id}_baseline_results.csv'),
            index=False
        )

        # 保存NWP结果
        nwp_results.to_csv(
            os.path.join(save_dir, f'station_{station_id}_nwp_results.csv'),
            index=False
        )

        # 保存改善率指标
        improvement_metrics.to_csv(
            os.path.join(save_dir, f'station_{station_id}_improvement_metrics.csv'),
            index=False
        )

        # 保存预测结果表格和对比曲线
        if station_id in all_prediction_data:
            test_times, y_true, baseline_predictions, nwp_predictions, is_daytime = all_prediction_data[station_id]

            # 保存预测结果表格
            save_prediction_results_table(test_times, y_true, baseline_predictions, nwp_predictions,
                                        is_daytime, station_id, save_dir)

            # 绘制预测对比曲线
            plot_prediction_comparison_curves(test_times, y_true, baseline_predictions, nwp_predictions,
                                            is_daytime, station_id, save_dir)

            # 绘制时间序列对比图（类似问题二格式）
            plot_time_series_comparison(test_times, y_true, baseline_predictions, nwp_predictions,
                                      station_id, save_dir)

    # 2. 创建汇总报告
    summary_report = []

    for station_id in STATIONS:
        if station_id not in all_station_results:
            continue

        baseline_results, nwp_results = all_station_results[station_id]
        improvement_metrics = all_improvement_metrics[station_id]

        # 找到最佳模型
        best_baseline = baseline_results.loc[baseline_results['R²'].idxmax()]
        best_nwp = nwp_results.loc[nwp_results['R²'].idxmax()]

        # 计算平均改善率
        avg_mae_improvement = improvement_metrics['MAE_Improvement_%'].mean()
        avg_rmse_improvement = improvement_metrics['RMSE_Improvement_%'].mean()
        avg_r2_improvement = improvement_metrics['R²_Improvement_%'].mean()

        summary_report.append({
            'Station': STATION_NAMES[station_id],
            'Best_Baseline_Model': best_baseline['Model'],
            'Best_Baseline_MAE': best_baseline['MAE'],
            'Best_Baseline_R²': best_baseline['R²'],
            'Best_NWP_Model': best_nwp['Model'],
            'Best_NWP_MAE': best_nwp['MAE'],
            'Best_NWP_R²': best_nwp['R²'],
            'Avg_MAE_Improvement_%': avg_mae_improvement,
            'Avg_RMSE_Improvement_%': avg_rmse_improvement,
            'Avg_R²_Improvement_%': avg_r2_improvement,
            'Models_with_Positive_MAE_Improvement': len(improvement_metrics[improvement_metrics['MAE_Improvement_%'] > 0]),
            'Total_Models': len(improvement_metrics)
        })

    summary_df = pd.DataFrame(summary_report)
    summary_df.to_csv(os.path.join(save_dir, 'q3_summary_report.csv'), index=False)

    return summary_df

def analyze_single_station(station_id):
    """分析单个站点的第二问和第三问对比"""
    print(f"\n{'='*60}")
    print(f"分析{STATION_NAMES[station_id]}（Station {station_id}）")
    print(f"{'='*60}")

    # 1. 加载数据
    df = load_station_data(station_id)
    if df is None:
        return None, None

    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['date_time'].min()} 到 {df['date_time'].max()}")

    # 2. 数据划分
    train_indices, test_indices = get_train_test_split_q3(df)
    print(f"训练集大小: {len(train_indices)}")
    print(f"测试集大小: {len(test_indices)}")

    # 3. 准备基准特征（第二问）
    print("\n=== 准备基准特征（第二问） ===")
    df_baseline = prepare_features_q3(df, include_nwp=False)

    # 4. 准备NWP增强特征（第三问）
    print("\n=== 准备NWP增强特征（第三问） ===")
    df_nwp = prepare_features_q3(df, include_nwp=True)

    # 检查NWP特征
    nwp_cols = [col for col in df_nwp.columns if col.startswith('nwp_') or 'theoretical' in col or 'relative_efficiency' in col]
    print(f"NWP相关特征数量: {len(nwp_cols)}")

    # 5. 构建基准模型（第二问）
    print("\n=== 构建基准模型（第二问） ===")
    baseline_feature_cols = [col for col in df_baseline.columns
                           if col not in ['power', 'date_time', 'date']]

    X_baseline = df_baseline[baseline_feature_cols]
    y = df_baseline['power']

    X_train_baseline = X_baseline.loc[train_indices]
    X_test_baseline = X_baseline.loc[test_indices]
    y_train = y.loc[train_indices]
    y_test = y.loc[test_indices]

    # 获取测试集的时间信息
    test_is_daytime = df_baseline.loc[test_indices, 'is_daytime']

    print(f"基准特征数量: {len(baseline_feature_cols)}")

    # 训练基准模型
    baseline_models, baseline_predictions = build_all_models_q3(X_train_baseline, y_train, X_test_baseline, 'baseline')

    # 添加Persistence基准
    test_dates = sorted(df_baseline.loc[test_indices, 'date'].unique())
    persistence_pred = persistence_model(df_baseline.loc[train_indices], test_dates)
    baseline_predictions['Persistence'] = persistence_pred

    # 评估基准模型
    baseline_results = evaluate_models_q3(y_test.values, baseline_predictions, test_is_daytime.values)

    # 6. 构建NWP增强模型（第三问）
    print("\n=== 构建NWP增强模型（第三问） ===")
    nwp_feature_cols = [col for col in df_nwp.columns
                       if col not in ['power', 'date_time', 'date']]

    X_nwp = df_nwp[nwp_feature_cols]

    X_train_nwp = X_nwp.loc[train_indices]
    X_test_nwp = X_nwp.loc[test_indices]

    print(f"NWP特征数量: {len(nwp_feature_cols)}")

    # 训练NWP模型
    nwp_models, nwp_predictions = build_all_models_q3(X_train_nwp, y_train, X_test_nwp, 'nwp')

    # 评估NWP模型
    nwp_results = evaluate_models_q3(y_test.values, nwp_predictions, test_is_daytime.values)

    # 7. 计算改善率指标
    improvement_metrics = calculate_improvement_metrics(baseline_results, nwp_results)

    # 8. 输出结果
    print(f"\n=== {STATION_NAMES[station_id]}分析结果 ===")
    print("\n基准模型（第二问）前5名:")
    baseline_top5 = baseline_results.nsmallest(5, 'MAE')
    for i, (_, row) in enumerate(baseline_top5.iterrows()):
        print(f"  {i+1}. {row['Model']}: MAE={row['MAE']:.4f}, R²={row['R²']:.4f}")

    print("\nNWP增强模型（第三问）前5名:")
    nwp_top5 = nwp_results.nsmallest(5, 'MAE')
    for i, (_, row) in enumerate(nwp_top5.iterrows()):
        print(f"  {i+1}. {row['Model']}: MAE={row['MAE']:.4f}, R²={row['R²']:.4f}")

    print("\n改善率统计:")
    positive_improvements = improvement_metrics[improvement_metrics['MAE_Improvement_%'] > 0]
    print(f"  MAE改善的模型数量: {len(positive_improvements)}/{len(improvement_metrics)}")
    print(f"  平均MAE改善率: {improvement_metrics['MAE_Improvement_%'].mean():.2f}%")
    print(f"  最大MAE改善率: {improvement_metrics['MAE_Improvement_%'].max():.2f}%")
    print(f"  最小MAE改善率: {improvement_metrics['MAE_Improvement_%'].min():.2f}%")

    # 9. 收集预测数据用于后续保存
    test_times = df_baseline.loc[test_indices, 'date_time']
    prediction_data = (test_times.values, y_test.values, baseline_predictions, nwp_predictions, test_is_daytime.values)

    return (baseline_results, nwp_results), improvement_metrics, prediction_data

def main():
    """主函数：执行问题3的全面分析"""
    print("=" * 80)
    print("问题3：融入NWP信息的光伏电站日前发电功率预测模型")
    print("四站点全面对比分析 - 使用第二问的所有12种模型")
    print("=" * 80)

    start_time = time.time()

    # 存储所有站点的结果
    all_station_results = {}
    all_improvement_metrics = {}
    all_prediction_data = {}

    # 分析每个站点
    for station_id in STATIONS:
        try:
            station_results, improvement_metrics, prediction_data = analyze_single_station(station_id)
            if station_results is not None:
                all_station_results[station_id] = station_results
                all_improvement_metrics[station_id] = improvement_metrics
                all_prediction_data[station_id] = prediction_data
        except Exception as e:
            print(f"分析站点{station_id}时出错: {e}")
            continue

    if not all_station_results:
        print("没有成功分析任何站点，程序退出。")
        return

    # 生成可视化图表
    print("\n=== 生成可视化图表 ===")
    plot_q3_comprehensive_analysis(all_station_results, all_improvement_metrics, RESULTS_DIR)
    print("✓ 统计分析图表已保存")

    # 生成预测对比图表
    plot_comprehensive_prediction_comparison(all_prediction_data, RESULTS_DIR)
    print("✓ 预测对比图表已保存")

    # 生成四站点时间序列对比图
    plot_four_stations_time_series(all_prediction_data, RESULTS_DIR)
    print("✓ 四站点时间序列对比图已保存")

    # 保存结果
    print("\n=== 保存分析结果 ===")
    summary_df = save_q3_results(all_station_results, all_improvement_metrics, all_prediction_data, RESULTS_DIR)
    print("✓ 分析结果已保存")

    # 生成综合预测结果表格
    print("\n=== 生成综合预测结果表格 ===")
    create_comprehensive_prediction_table(all_prediction_data, RESULTS_DIR)
    print("✓ 综合预测结果表格已保存")

    # 生成综合分析报告
    print("\n" + "=" * 80)
    print("问题3：融入NWP信息效果的综合分析结论")
    print("=" * 80)

    print("\n【四站点汇总结果】")
    print(summary_df.to_string(index=False))

    # 计算总体统计
    total_models_analyzed = summary_df['Total_Models'].sum()
    total_positive_improvements = summary_df['Models_with_Positive_MAE_Improvement'].sum()
    overall_positive_rate = total_positive_improvements / total_models_analyzed * 100

    avg_mae_improvement = summary_df['Avg_MAE_Improvement_%'].mean()
    avg_r2_improvement = summary_df['Avg_R²_Improvement_%'].mean()

    print(f"\n【总体统计】")
    print(f"分析站点数量: {len(all_station_results)}")
    print(f"总分析模型数量: {total_models_analyzed}")
    print(f"MAE改善的模型数量: {total_positive_improvements}")
    print(f"MAE改善率: {overall_positive_rate:.1f}%")
    print(f"平均MAE改善率: {avg_mae_improvement:.2f}%")
    print(f"平均R²改善率: {avg_r2_improvement:.2f}%")

    print(f"\n【结论】")
    if avg_mae_improvement > 5:
        print("✅ 融入NWP信息能够有效提高光伏发电功率预测精度")
        print("   - 建议在实际应用中使用融入NWP信息的模型")
        print("   - 特别是在长期预测和极端天气条件下")
    elif avg_mae_improvement > 0:
        print("⚠️  融入NWP信息对预测精度有小幅提升")
        print("   - 提升效果有限，需要权衡计算成本和精度收益")
        print("   - 可以在对精度要求极高的场景中考虑使用")
    else:
        print("❌ 融入NWP信息对预测精度提升有限或无效果")
        print("   - 可能原因：NWP数据质量、特征工程不足、时间尺度不匹配")
        print("   - 建议优化NWP特征工程或考虑其他改进方法")

    # 最佳实践建议
    print(f"\n【最佳实践建议】")

    # 找到表现最好的站点和模型
    best_station_idx = summary_df['Avg_MAE_Improvement_%'].idxmax()
    best_station = summary_df.iloc[best_station_idx]

    print(f"1. 最佳表现站点: {best_station['Station']}")
    print(f"   - 平均MAE改善率: {best_station['Avg_MAE_Improvement_%']:.2f}%")
    print(f"   - 最佳基准模型: {best_station['Best_Baseline_Model']}")
    print(f"   - 最佳NWP模型: {best_station['Best_NWP_Model']}")

    print(f"\n2. 模型选择建议:")
    print(f"   - 对于基准预测：优先使用梯度提升类模型")
    print(f"   - 对于NWP增强：根据具体站点特性选择最适合的模型")
    print(f"   - 建议进行站点特定的模型优化")

    print(f"\n3. 特征工程建议:")
    print(f"   - 重点优化NWP特征的时间滞后和滑动统计")
    print(f"   - 考虑引入更多物理约束的特征")
    print(f"   - 探索NWP数据的质量控制和预处理方法")

    # 运行时间
    end_time = time.time()
    total_time = end_time - start_time
    print(f"\n总运行时间: {total_time:.2f}秒")

    print(f"\n所有结果已保存到目录: {RESULTS_DIR}")
    print("=" * 80)

if __name__ == "__main__":
    main()
